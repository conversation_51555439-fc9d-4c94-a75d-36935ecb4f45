/**
 * @file Cleanup Utilities - Consolidated Helper Functions & Validation
 * @filepath shared/src/base/modules/cleanup/CleanupUtilities.ts
 * @task-id M-TSK-01.SUB-01.REF-01.UTILITIES
 * @component cleanup-utilities
 * @created 2025-07-24 16:32:57 +03
 * @updated 2025-07-25 14:15:00 +03
 * 
 * @description
 * Consolidated utility functions and validation helpers for enhanced cleanup operations
 * with Jest compatibility and performance optimization. Now serves as the coordination
 * layer for extracted utility modules.
 * 
 * REFACTORING NOTES:
 * - Extracted validation utilities to UtilityValidation.ts
 * - Extracted execution utilities to UtilityExecution.ts  
 * - Extracted analysis utilities to UtilityAnalysis.ts
 * - Extracted performance utilities to UtilityPerformance.ts
 * - Maintains template utilities and export collections
 * 
 * LESSONS LEARNED INTEGRATION:
 * - Jest compatibility: Safe utility functions without timing dependencies
 * - Memory safety: Efficient helper functions with proper cleanup
 * - Performance: Optimized algorithms for common operations
 * - Testing: Mock-friendly utility patterns
 */

import { CleanupPriority, CleanupOperationType } from '../../CleanupCoordinator';
import {
  ICleanupTemplate,
  ICleanupTemplateStep,
  IValidationResult,
  ITemplateExecutionContext,
  IEnhancedCleanupConfig
} from '../../types/CleanupTypes';
import { DEFAULT_TEMPLATE_CONSTANTS } from './CleanupConfiguration';

// Import from extracted modules
import { 
  validateTemplate,
  evaluateStepCondition,
  validateConfigurationCompleteness,
  ValidationUtils
} from './UtilityValidation';

import {
  generateExecutionId,
  generateCheckpointId,
  findMatchingComponents,
  estimateOperationDuration,
  sortOperationsByDependencies,
  ExecutionUtils
} from './UtilityExecution';

import {
  generateDependencyCacheKey,
  identifyOptimizationOpportunities,
  generateMitigationStrategies,
  generateContingencyPlans,
  AnalysisUtils
} from './UtilityAnalysis';

import {
  calculateChecksum,
  deepClone,
  getNestedProperty,
  formatDuration,
  sanitizeForLogging,
  PerformanceUtils
} from './UtilityPerformance';

// ============================================================================
// TEMPLATE UTILITIES (CORE COORDINATION FUNCTIONS)
// ============================================================================

/**
 * Create default template step
 */
export function createDefaultTemplateStep(
  id: string,
  operationName: string,
  componentPattern: string = '.*'
): ICleanupTemplateStep {
  return {
    id,
    type: CleanupOperationType.RESOURCE_CLEANUP,
    componentPattern,
    operationName,
    parameters: {},
    timeout: DEFAULT_TEMPLATE_CONSTANTS.DEFAULT_STEP_TIMEOUT,
    retryPolicy: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2.0,
      maxRetryDelay: 10000,
      retryOnErrors: ['TimeoutError', 'ResourceBusyError']
    },
    dependsOn: [],
    priority: DEFAULT_TEMPLATE_CONSTANTS.DEFAULT_PRIORITY,
    estimatedDuration: 5000,
    description: `Execute ${operationName} on matching components`
  };
}

/**
 * Merge template execution contexts
 */
export function mergeExecutionContexts(
  base: ITemplateExecutionContext,
  updates: Partial<ITemplateExecutionContext>
): ITemplateExecutionContext {
  return {
    ...base,
    ...updates,
    parameters: { ...base.parameters, ...updates.parameters },
    systemState: { ...base.systemState, ...updates.systemState }
  };
}

// ============================================================================
// RE-EXPORTED FUNCTIONS FOR BACKWARD COMPATIBILITY
// ============================================================================

// Template validation functions
export {
  validateTemplate,
  evaluateStepCondition,
  validateConfigurationCompleteness
};

// Execution functions
export {
  generateExecutionId,
  generateCheckpointId,
  findMatchingComponents,
  estimateOperationDuration,
  sortOperationsByDependencies
};

// Analysis functions
export {
  generateDependencyCacheKey,
  identifyOptimizationOpportunities,
  generateMitigationStrategies,
  generateContingencyPlans
};

// Performance functions
export {
  calculateChecksum,
  deepClone,
  getNestedProperty,
  formatDuration,
  sanitizeForLogging
};

// ============================================================================
// EXPORT UTILITY COLLECTIONS
// ============================================================================

/**
 * Collection of template utilities
 */
export const TemplateUtils = {
  createDefaultTemplateStep,
  mergeExecutionContexts
};

// Re-export utility collections from extracted modules
export { ValidationUtils } from './UtilityValidation';
export { ExecutionUtils } from './UtilityExecution';
export { AnalysisUtils } from './UtilityAnalysis';
export { PerformanceUtils } from './UtilityPerformance'; 