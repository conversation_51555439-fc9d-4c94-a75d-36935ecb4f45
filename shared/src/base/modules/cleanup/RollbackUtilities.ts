/**
 * @file Rollback Utilities - Helper Functions and Assessment Logic
 * @filepath shared/src/base/modules/cleanup/RollbackUtilities.ts
 * @task-id M-TSK-01.SUB-01.REF-01.ROLLBACK.UTILITIES
 * @component rollback-utilities
 * @created 2025-07-25 14:30:00 +03
 * 
 * @description
 * Extracted utility functions for rollback management operations.
 * Implements assessment, validation, and helper functions with Jest compatibility.
 * 
 * EXTRACTION RATIONALE:
 * - Domain separation: Utility functions isolated from core rollback logic
 * - Reusability: Helper functions can be used across rollback components
 * - Testability: Independent utility unit testing
 * - Maintainability: Clear separation of concerns
 */

import {
  ICheckpoint,
  IRollbackAction,
  ISystemSnapshot
} from '../../types/CleanupTypes';

// ============================================================================
// ID GENERATION UTILITIES
// ============================================================================

/**
 * Generate unique checkpoint ID
 */
export function generateCheckpointId(operationId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `checkpoint-${operationId}-${timestamp}-${random}`;
}

// ============================================================================
// DATA MANIPULATION UTILITIES
// ============================================================================

/**
 * Deep clone an object safely
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * Calculate checkpoint checksum with comprehensive data inclusion
 * Enhanced following Anti-Simplification policy with ES6 features
 * Deterministic for same data, unique for different data
 * 
 * FIXED: Simplified and more reliable hashing algorithm using crypto-style approach
 */
export async function calculateCheckpointChecksum(
  state: any, 
  actions: IRollbackAction[], 
  snapshot: ISystemSnapshot
): Promise<string> {
  // Create a comprehensive data structure that captures all differences
  const checksumComponents = [
    // State component - serialize with sorted keys for determinism
    `STATE:${JSON.stringify(state, Object.keys(state || {}).sort())}`,
    
    // Actions component - capture all action details
    `ACTIONS_COUNT:${actions.length}`,
    `ACTIONS_TYPES:${actions.map(a => a.type).sort().join(',')}`,
    `ACTIONS_PRIORITIES:${actions.map(a => a.priority).join(',')}`,
    `ACTIONS_DURATIONS:${actions.map(a => a.estimatedDuration).join(',')}`,
    `ACTIONS_CRITICAL:${actions.map(a => a.critical).join(',')}`,
    `ACTIONS_DESCRIPTIONS:${actions.map(a => a.description || '').sort().join('|')}`,
    `ACTIONS_PARAMS:${actions.map(a => JSON.stringify(a.parameters, Object.keys(a.parameters || {}).sort())).sort().join('|')}`,
    
    // Snapshot component - capture all snapshot details
    `SNAPSHOT_TIMESTAMP:${snapshot.timestamp.getTime()}`,
    `SNAPSHOT_VERSION:${snapshot.version}`,
    `SNAPSHOT_METRICS:${JSON.stringify(snapshot.systemMetrics, Object.keys(snapshot.systemMetrics || {}).sort())}`,
    `SNAPSHOT_ACTIVE_OPS:${snapshot.activeOperations.slice().sort().join(',')}`,
    `SNAPSHOT_COMPONENT_STATES:${Array.from(snapshot.componentStates.entries()).map(([k, v]) => `${k}=${JSON.stringify(v)}`).sort().join('|')}`,
    `SNAPSHOT_RESOURCE_STATES:${Array.from(snapshot.resourceStates.entries()).map(([k, v]) => `${k}=${JSON.stringify(v)}`).sort().join('|')}`,
    `SNAPSHOT_CONFIG_STATES:${Array.from(snapshot.configurationStates.entries()).map(([k, v]) => `${k}=${JSON.stringify(v)}`).sort().join('|')}`
  ];
  
  // Join all components into a single string for hashing
  const dataToHash = checksumComponents.join('\n');
  
  // Use a robust hash function (DJB2 algorithm)
  let hash = 5381;
  for (let i = 0; i < dataToHash.length; i++) {
    hash = ((hash << 5) + hash) + dataToHash.charCodeAt(i); // hash * 33 + char
  }
  
  // Convert to unsigned 32-bit and create additional hash components
  const primaryHash = (hash >>> 0);
  
  // Create secondary hash using different approach for additional entropy
  let secondaryHash = dataToHash.length;
  for (let i = 0; i < Math.min(dataToHash.length, 100); i++) {
    secondaryHash = ((secondaryHash << 3) + secondaryHash) + dataToHash.charCodeAt(i);
  }
  secondaryHash = (secondaryHash >>> 0);
  
  // Combine hashes and convert to base36 for compactness
  const combinedHash = `${primaryHash.toString(36)}-${secondaryHash.toString(36)}-${dataToHash.length.toString(36)}`;
  
  // Create final base64 checksum
  const finalChecksum = Buffer.from(combinedHash).toString('base64');
  
  // Return first 16 characters for consistent length
  return finalChecksum.substring(0, 16);
}

// ============================================================================
// ROLLBACK ACTION UTILITIES
// ============================================================================

/**
 * Sort rollback actions by priority and duration
 * LESSON LEARNED: Optimized sorting to prevent hanging operations
 */
export function sortRollbackActions(actions: IRollbackAction[]): IRollbackAction[] {
  return [...actions].sort((a, b) => {
    // Primary sort: priority (higher priority first)
    const priorityDiff = b.priority - a.priority;
    if (priorityDiff !== 0) return priorityDiff;
    
    // Secondary sort: estimated duration (longer operations first for early failure detection)
    return b.estimatedDuration - a.estimatedDuration;
  });
}

// ============================================================================
// ASSESSMENT UTILITIES
// ============================================================================

/**
 * Assess rollback complexity based on checkpoint characteristics
 */
export function assessRollbackComplexity(checkpoint: ICheckpoint): 'simple' | 'moderate' | 'complex' {
  const actionCount = checkpoint.rollbackActions.length;
  if (actionCount <= 3) return 'simple';
  if (actionCount <= 10) return 'moderate';
  return 'complex';
}

/**
 * Estimate rollback execution time
 */
export function estimateRollbackTime(checkpoint: ICheckpoint): number {
  return checkpoint.rollbackActions.reduce((total, action) => total + action.estimatedDuration, 0);
}

/**
 * Assess rollback risk level
 */
export function assessRollbackRisk(checkpoint: ICheckpoint): 'low' | 'medium' | 'high' {
  const criticalActions = checkpoint.rollbackActions.filter(a => a.critical).length;
  const ageHours = (Date.now() - checkpoint.timestamp.getTime()) / (1000 * 60 * 60);
  
  if (criticalActions === 0 && ageHours < 1) return 'low';
  if (criticalActions <= 2 && ageHours < 24) return 'medium';
  return 'high';
}

/**
 * Identify rollback limitations
 */
export function identifyRollbackLimitations(checkpoint: ICheckpoint): string[] {
  const limitations: string[] = [];
  
  const ageHours = (Date.now() - checkpoint.timestamp.getTime()) / (1000 * 60 * 60);
  if (ageHours > 24) {
    limitations.push('Checkpoint is older than 24 hours - may have stale state');
  }

  const criticalActions = checkpoint.rollbackActions.filter(a => a.critical).length;
  if (criticalActions > 5) {
    limitations.push('High number of critical actions - increased failure risk');
  }

  return limitations;
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate checkpoint integrity
 */
export function validateCheckpointIntegrity(checkpoint: ICheckpoint): boolean {
  if (!checkpoint.id || !checkpoint.operationId) return false;
  if (!checkpoint.timestamp || checkpoint.timestamp > new Date()) return false;
  if (!Array.isArray(checkpoint.rollbackActions)) return false;
  
  return true;
}

/**
 * Validate rollback action
 */
export function validateRollbackAction(action: IRollbackAction): boolean {
  if (!action.type || !action.parameters) return false;
  if (action.priority < 0 || action.priority > 10) return false;
  if (action.estimatedDuration < 0) return false;
  
  return true;
}

// ============================================================================
// UTILITY COLLECTION
// ============================================================================

/**
 * Collection of rollback utilities
 */
export const RollbackUtils = {
  generateCheckpointId,
  deepClone,
  calculateCheckpointChecksum,
  sortRollbackActions,
  assessRollbackComplexity,
  estimateRollbackTime,
  assessRollbackRisk,
  identifyRollbackLimitations,
  validateCheckpointIntegrity,
  validateRollbackAction
}; 