/**
 * @file Utility Analysis - Dependency and Optimization Analysis
 * @filepath shared/src/base/modules/cleanup/UtilityAnalysis.ts
 * @task-id M-TSK-01.SUB-01.REF-01.UTILITIES.ANALYSIS
 * @component utility-analysis
 * @created 2025-07-25 14:15:00 +03
 * 
 * @description
 * Extracted analysis utilities for enhanced cleanup operations.
 * Implements dependency analysis, optimization opportunities, and risk mitigation strategies.
 * 
 * EXTRACTION RATIONALE:
 * - Domain separation: Analysis logic isolated from execution utilities
 * - Intelligence: Advanced optimization and risk assessment algorithms
 * - Testability: Independent analysis unit testing
 * - Reusability: Analysis functions used across optimization components
 */

import { CleanupPriority } from '../../CleanupCoordinator';
import { ICleanupOperation } from '../../CleanupCoordinator';
import {
  IOptimizationOpportunity,
  IRiskFactor
} from '../../types/CleanupTypes';

// ============================================================================
// DEPENDENCY CACHE UTILITIES
// ============================================================================

/**
 * Generate dependency analysis cache key
 */
export function generateDependencyCacheKey(operations: ICleanupOperation[]): string {
  const operationSignature = operations
    .map(op => `${op.id}:${(op.dependencies || []).sort().join(',')}`)
    .sort()
    .join('|');
  
  // Simple hash for cache key
  let hash = 0;
  for (let i = 0; i < operationSignature.length; i++) {
    const char = operationSignature.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return `dep-analysis-${Math.abs(hash)}`;
}

// ============================================================================
// OPTIMIZATION ANALYSIS UTILITIES
// ============================================================================

/**
 * Identify optimization opportunities
 */
export function identifyOptimizationOpportunities(
  operations: ICleanupOperation[],
  parallelGroups: string[][]
): IOptimizationOpportunity[] {
  const opportunities: IOptimizationOpportunity[] = [];

  // Look for parallelization opportunities
  for (const group of parallelGroups) {
    if (group.length > 1) {
      opportunities.push({
        type: 'parallelization',
        description: `Parallel execution of ${group.length} operations`,
        estimatedImprovement: Math.min(50, group.length * 10), // Up to 50% improvement
        implementationComplexity: 'medium',
        riskLevel: 'low',
        affectedOperations: group
      });
    }
  }

  // Look for priority adjustment opportunities
  const highPriorityOps = operations.filter(op => op.priority === CleanupPriority.EMERGENCY);
  const lowPriorityOps = operations.filter(op => op.priority === CleanupPriority.LOW);
  
  if (highPriorityOps.length > lowPriorityOps.length * 2) {
    opportunities.push({
      type: 'priority_adjustment',
      description: 'Consider rebalancing operation priorities for better performance',
      estimatedImprovement: 15,
      implementationComplexity: 'low',
      riskLevel: 'low',
      affectedOperations: highPriorityOps.map(op => op.id)
    });
  }

  return opportunities;
}

// ============================================================================
// RISK MITIGATION UTILITIES
// ============================================================================

/**
 * Generate mitigation strategies for risk factors
 */
export function generateMitigationStrategies(riskFactors: IRiskFactor[]): string[] {
  const strategies: string[] = [];
  
  riskFactors.forEach(factor => {
    switch (factor.type) {
      case 'circular_dependency':
        strategies.push('Review and refactor operation dependencies to eliminate cycles');
        break;
      case 'resource_contention':
        strategies.push('Consider implementing resource pooling or queue management');
        break;
      case 'timing_constraint':
        strategies.push('Optimize operation timeouts and implement parallel execution');
        break;
      case 'external_dependency':
        strategies.push('Implement fallback mechanisms and health check monitoring');
        break;
    }
  });

  // Remove duplicates using ES6+ Set and spread operator
  return Array.from(new Set(strategies));
}

/**
 * Generate contingency plans for risk factors
 */
export function generateContingencyPlans(riskFactors: IRiskFactor[]): string[] {
  const plans: string[] = [];
  
  if (riskFactors.some(f => f.severity === 'critical')) {
    plans.push('Emergency rollback procedures should be prepared');
    plans.push('Manual intervention procedures should be documented');
  }
  
  if (riskFactors.some(f => f.type === 'resource_contention')) {
    plans.push('Alternative resource allocation strategies should be available');
  }

  if (riskFactors.some(f => f.type === 'timing_constraint')) {
    plans.push('Timeout extension protocols should be established');
  }

  // Default contingency plans
  plans.push('Comprehensive logging and monitoring for failure analysis');
  plans.push('Automated alert system for dependency analysis anomalies');

  return Array.from(new Set(plans));
}

// ============================================================================
// ANALYSIS UTILITY COLLECTION
// ============================================================================

/**
 * Collection of analysis utilities
 */
export const AnalysisUtils = {
  generateDependencyCacheKey,
  identifyOptimizationOpportunities,
  generateMitigationStrategies,
  generateContingencyPlans
}; 