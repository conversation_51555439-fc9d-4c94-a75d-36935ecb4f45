/**
 * @file Utility Execution - Execution and Operation Utilities
 * @filepath shared/src/base/modules/cleanup/UtilityExecution.ts
 * @task-id M-TSK-01.SUB-01.REF-01.UTILITIES.EXECUTION
 * @component utility-execution
 * @created 2025-07-25 14:15:00 +03
 * 
 * @description
 * Extracted execution utilities for enhanced cleanup operations.
 * Implements ID generation, component matching, and operation sorting with Jest compatibility.
 * 
 * EXTRACTION RATIONALE:
 * - Domain separation: Execution logic isolated from validation utilities
 * - Performance: Optimized algorithms for operation ordering
 * - Testability: Independent execution unit testing
 * - Reusability: Execution functions used across workflow components
 */

import { CleanupPriority, CleanupOperationType } from '../../CleanupCoordinator';
import { ICleanupOperation } from '../../CleanupCoordinator';

// ============================================================================
// ID GENERATION UTILITIES
// ============================================================================

/**
 * Generate unique execution ID
 */
export function generateExecutionId(templateId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `template-exec-${templateId}-${timestamp}-${random}`;
}

/**
 * Generate unique checkpoint ID
 */
export function generateCheckpointId(operationId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `checkpoint-${operationId}-${timestamp}-${random}`;
}

// ============================================================================
// COMPONENT MATCHING UTILITIES
// ============================================================================

/**
 * Find components matching a pattern
 * LESSON LEARNED: Safe regex matching with fallback
 */
export function findMatchingComponents(pattern: string, components: string[]): string[] {
  try {
    const regex = new RegExp(pattern, 'i');
    return components.filter(component => regex.test(component));
  } catch (error) {
    // Fallback to simple string matching if regex fails
    return components.filter(component => 
      component.toLowerCase().includes(pattern.toLowerCase())
    );
  }
}

// ============================================================================
// OPERATION ESTIMATION UTILITIES
// ============================================================================

/**
 * Estimate operation duration based on type and priority
 */
export function estimateOperationDuration(operation: ICleanupOperation | undefined): number {
  if (!operation) return 1000; // Default 1 second
  
  // Base estimation on operation type
  let baseTime = 1000;
  if (operation.type.toString().includes('CLEANUP')) baseTime = 2000;
  else if (operation.type.toString().includes('AUDIT')) baseTime = 500;
  else if (operation.type.toString().includes('OPTIMIZATION')) baseTime = 3000;
  
  // Apply priority multiplier
  const priorityMultiplier = operation.priority === CleanupPriority.EMERGENCY ? 0.5 : 1.0;
  
  return Math.max(100, baseTime * priorityMultiplier);
}

// ============================================================================
// OPERATION SORTING UTILITIES
// ============================================================================

/**
 * Sort operations by dependency order
 * LESSON LEARNED: Optimized sorting to prevent infinite loops
 */
export function sortOperationsByDependencies(operations: ICleanupOperation[]): ICleanupOperation[] {
  const sorted: ICleanupOperation[] = [];
  const visited = new Set<string>();
  const visiting = new Set<string>();

  const visit = (operation: ICleanupOperation): void => {
    if (visiting.has(operation.id)) {
      // Circular dependency detected - skip this operation
      return;
    }
    
    if (visited.has(operation.id)) {
      return;
    }

    visiting.add(operation.id);

    // Visit dependencies first
    if (operation.dependencies) {
      operation.dependencies.forEach(depId => {
        const depOp = operations.find(op => op.id === depId);
        if (depOp) {
          visit(depOp);
        }
      });
    }

    visiting.delete(operation.id);
    visited.add(operation.id);
    sorted.push(operation);
  };

  operations.forEach(operation => {
    if (!visited.has(operation.id)) {
      visit(operation);
    }
  });

  return sorted;
}

// ============================================================================
// EXECUTION UTILITY COLLECTION
// ============================================================================

/**
 * Collection of execution utilities  
 */
export const ExecutionUtils = {
  generateExecutionId,
  generateCheckpointId,
  findMatchingComponents,
  estimateOperationDuration,
  sortOperationsByDependencies
}; 