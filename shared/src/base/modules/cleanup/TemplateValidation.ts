/**
 * @file Template Validation - Comprehensive Template Validation System
 * @filepath shared/src/base/modules/cleanup/TemplateValidation.ts
 * @task-id M-TSK-01.SUB-01.REF-01.TEMPLATE-VALIDATION
 * @component template-validation
 * @created 2025-01-20 16:50:00 +03
 * 
 * @description
 * Comprehensive validation system for cleanup templates providing structure validation,
 * dependency validation, condition evaluation, and enterprise-grade quality checks.
 * 
 * LESSONS LEARNED INTEGRATION:
 * - ES6+ compliance: Uses modern iteration patterns throughout
 * - Jest compatibility: Synchronous validation with async yielding
 * - Memory safety: Efficient validation without resource allocation
 */

import { SimpleLogger } from '../../LoggingMixin';
import {
  ICleanupTemplate,
  ICleanupTemplateStep,
  IValidationResult,
  IValidationIssue,
  IStepCondition,
  IStepExecutionContext,
  IComponentRegistry
} from '../../types/CleanupTypes';
import { DependencyGraph, validateDependencyGraph } from './TemplateDependencies';

/**
 * ============================================================================
 * AI CONTEXT: Comprehensive Template Validation System
 * Purpose: Validate template structure, dependencies, and execution conditions
 * Complexity: Moderate - Multi-layer validation with enterprise quality checks
 * AI Navigation: 5 logical sections - Structure, Dependencies, Conditions, Utilities, Export
 * ============================================================================
 */

/**
 * ============================================================================
 * SECTION 1: CORE VALIDATION INTERFACES & TYPES (Lines 1-50)
 * AI Context: "Validation result types and configuration interfaces"
 * ============================================================================
 */

export interface ITemplateValidationConfig {
  strictMode: boolean;
  validateDependencies: boolean;
  validateConditions: boolean;
  validateParameters: boolean;
  maxOperationCount: number;
  maxDependencyDepth: number;
  allowedOperationTypes: string[];
}

export interface IExtendedValidationResult extends IValidationResult {
  performanceMetrics: {
    validationTime: number;
    checksPerformed: number;
    dependencyComplexity: number;
  };
  recommendations: string[];
  qualityScore: number;
}

/**
 * ============================================================================
 * SECTION 2: TEMPLATE STRUCTURE VALIDATION (Lines 51-200)
 * AI Context: "Basic template structure and format validation"
 * ============================================================================
 */

/**
 * Enhanced Template Validator
 * 
 * Provides comprehensive template validation with enterprise-grade quality checks
 */
export class TemplateValidator {
  private _logger: SimpleLogger;
  private _config: ITemplateValidationConfig;

  constructor(config: Partial<ITemplateValidationConfig> = {}) {
    this._logger = new SimpleLogger('TemplateValidator');
    this._config = {
      strictMode: true,
      validateDependencies: true,
      validateConditions: true,
      validateParameters: true,
      maxOperationCount: 100,
      maxDependencyDepth: 20,
      allowedOperationTypes: [
        'cleanup',
        'validation',
        'preparation',
        'finalization',
        'rollback',
        'notification'
      ],
      ...config
    };
  }

  /**
   * Comprehensive template validation
   */
  async validateTemplate(template: ICleanupTemplate): Promise<IExtendedValidationResult> {
    const startTime = performance.now();
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    this._logger.logDebug('Starting template validation', {
      templateId: template.id,
      operationCount: template.operations.length,
      strictMode: this._config.strictMode
    });

    try {
      // LESSON LEARNED: Async yielding for Jest compatibility
      await Promise.resolve();

      // Basic structure validation
      const structureResult = await this._validateTemplateStructure(template);
      issues.push(...structureResult.issues);
      warnings.push(...structureResult.warnings);
      recommendations.push(...structureResult.recommendations);
      checksPerformed += structureResult.checksPerformed;

      // Operation validation
      const operationResult = await this._validateOperations(template.operations);
      issues.push(...operationResult.issues);
      warnings.push(...operationResult.warnings);
      recommendations.push(...operationResult.recommendations);
      checksPerformed += operationResult.checksPerformed;

      // Dependency validation
      if (this._config.validateDependencies) {
        const dependencyResult = await this._validateDependencies(template);
        issues.push(...dependencyResult.issues);
        warnings.push(...dependencyResult.warnings);
        recommendations.push(...dependencyResult.recommendations);
        checksPerformed += dependencyResult.checksPerformed;
      }

      // Condition validation
      if (this._config.validateConditions) {
        const conditionResult = await this._validateConditions(template.operations);
        issues.push(...conditionResult.issues);
        warnings.push(...conditionResult.warnings);
        recommendations.push(...conditionResult.recommendations);
        checksPerformed += conditionResult.checksPerformed;
      }

      // Parameter validation
      if (this._config.validateParameters) {
        const parameterResult = await this._validateParameters(template);
        issues.push(...parameterResult.issues);
        warnings.push(...parameterResult.warnings);
        recommendations.push(...parameterResult.recommendations);
        checksPerformed += parameterResult.checksPerformed;
      }

      const validationTime = performance.now() - startTime;
      const qualityScore = this._calculateQualityScore(template, issues, warnings);

      const result: IExtendedValidationResult = {
        valid: issues.filter(i => i.severity === 'error').length === 0,
        issues,
        warnings,
        suggestions: recommendations,
        performanceMetrics: {
          validationTime,
          checksPerformed,
          dependencyComplexity: this._calculateDependencyComplexity(template)
        },
        recommendations,
        qualityScore
      };

      this._logger.logInfo('Template validation completed', {
        templateId: template.id,
        valid: result.valid,
        errorCount: issues.filter(i => i.severity === 'error').length,
        warningCount: warnings.length,
        qualityScore,
        validationTime
      });

      return result;

    } catch (error) {
      const validationError = error instanceof Error ? error : new Error(String(error));
      this._logger.logError('Template validation failed', validationError, {
        templateId: template.id
      });

      return {
        valid: false,
        issues: [{
          type: 'validation_error',
          message: `Validation failed: ${validationError.message}`,
          severity: 'error'
        }],
        warnings: [],
        suggestions: [],
        performanceMetrics: {
          validationTime: performance.now() - startTime,
          checksPerformed,
          dependencyComplexity: 0
        },
        recommendations: [],
        qualityScore: 0
      };
    }
  }

  /**
   * ============================================================================
   * SECTION 3: DEPENDENCY VALIDATION (Lines 201-350)
   * AI Context: "Template dependency validation and cycle detection"
   * ============================================================================
   */

  private async _validateDependencies(template: ICleanupTemplate): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    try {
      // Create dependency graph
      const graph = new DependencyGraph();
      
      // Add all operations as nodes
      template.operations.forEach(op => {
        graph.addNode(op.id);
        checksPerformed++;
      });

      // Add dependencies
      template.operations.forEach(op => {
        if (op.dependsOn && op.dependsOn.length > 0) {
          // Validate dependency references
          op.dependsOn.forEach(depId => {
            const dependencyExists = template.operations.some(o => o.id === depId);
            if (!dependencyExists) {
              issues.push({
                type: 'invalid_dependency',
                message: `Operation ${op.id} depends on non-existent operation ${depId}`,
                severity: 'error'
              });
            }
            checksPerformed++;
          });

          graph.addDependency(op.id, op.dependsOn);
        }
      });

      // Validate dependency graph
      const graphValidation = validateDependencyGraph(graph);
      
      if (!graphValidation.valid) {
        graphValidation.issues.forEach(issue => {
          issues.push({
            type: 'dependency_graph_error',
            message: issue,
            severity: 'error'
          });
        });
      }

      graphValidation.warnings.forEach(warning => {
        warnings.push(warning);
      });

      // Check dependency depth
      const criticalPath = graph.getCriticalPath();
      if (criticalPath.length > this._config.maxDependencyDepth) {
        warnings.push(`Dependency chain is deep (${criticalPath.length} steps). Consider parallel execution.`);
        recommendations.push('Consider breaking down complex dependencies into parallel groups');
      }

      // Check for parallel execution opportunities
      const parallelGroups = graph.getParallelGroups();
      if (parallelGroups.length > 1) {
        recommendations.push(`Template can be optimized with ${parallelGroups.length} parallel execution groups`);
      }

      checksPerformed += 5; // Graph validation checks

    } catch (error) {
      issues.push({
        type: 'dependency_validation_error',
        message: `Dependency validation error: ${error instanceof Error ? error.message : String(error)}`,
        severity: 'error'
      });
    }

    return { issues, warnings, recommendations, checksPerformed };
  }

  /**
   * ============================================================================
   * SECTION 4: CONDITION & PARAMETER VALIDATION (Lines 351-500)
   * AI Context: "Step condition evaluation and parameter validation"
   * ============================================================================
   */

  private async _validateConditions(operations: ICleanupTemplateStep[]): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    // ES6+ COMPLIANT: Use forEach instead of for...of
    operations.forEach(operation => {
      if (operation.condition) {
        const conditionValid = this._validateStepCondition(operation.condition);
        if (!conditionValid.valid) {
          issues.push({
            type: 'invalid_condition',
            message: `Operation ${operation.id} has invalid condition: ${conditionValid.reason}`,
            severity: 'error'
          });
        }
        checksPerformed++;
      }

      // Validate condition logic
      if (operation.condition?.type === 'custom' && !operation.condition.customCondition) {
        issues.push({
          type: 'missing_condition_expression',
          message: `Operation ${operation.id} has custom condition but no customCondition function`,
          severity: 'error'
        });
      }

      // Check for component exists conditions
      if (operation.condition?.type === 'component_exists' && !operation.condition.componentId) {
        issues.push({
          type: 'missing_component_id',
          message: `Operation ${operation.id} has component_exists condition but no componentId`,
          severity: 'error'
        });
      }

      checksPerformed++;
    });

    return { issues, warnings, recommendations, checksPerformed };
  }

  private async _validateParameters(template: ICleanupTemplate): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    // Validate template-level metadata
    if (template.metadata) {
      Object.entries(template.metadata).forEach(([key, value]) => {
        if (key.trim().length === 0) {
          warnings.push('Template metadata keys should not be empty');
        }

        if (typeof value === 'object' && value !== null && Object.keys(value).length === 0) {
          warnings.push(`Template metadata '${key}' is an empty object`);
        }

        checksPerformed++;
      });
    }

    // Validate operation parameters
    template.operations.forEach(operation => {
      if (operation.parameters) {
        Object.entries(operation.parameters).forEach(([key, value]) => {
          if (key.trim().length === 0) {
            issues.push({
              type: 'invalid_parameter_name',
              message: `Operation ${operation.id} has empty parameter name`,
              severity: 'error'
            });
          }

          // Check for template references in parameters
          if (typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {
            const templateRef = value.slice(2, -1);
            // For now, just warn about template references - could be expanded to validate against template metadata
            warnings.push(`Operation ${operation.id} parameter '${key}' uses template reference '${templateRef}'`);
          }

          checksPerformed++;
        });
      }
    });

    return { issues, warnings, recommendations, checksPerformed };
  }

  /**
   * ============================================================================
   * SECTION 5: VALIDATION UTILITIES (Lines 501-650)
   * AI Context: "Helper methods for structure validation and quality scoring"
   * ============================================================================
   */

  private async _validateTemplateStructure(template: ICleanupTemplate): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    // Basic structure validation
    if (!template.id || template.id.trim().length === 0) {
      issues.push({
        type: 'missing_id',
        message: 'Template must have a valid ID',
        severity: 'error'
      });
    }
    checksPerformed++;

    if (!template.name || template.name.trim().length === 0) {
      warnings.push('Template should have a descriptive name');
      recommendations.push('Add a descriptive name for better template identification');
    }
    checksPerformed++;

    if (!template.description || template.description.trim().length === 0) {
      warnings.push('Template should have a description');
      recommendations.push('Add a description explaining the template purpose and usage');
    }
    checksPerformed++;

    if (!template.operations || template.operations.length === 0) {
      issues.push({
        type: 'no_operations',
        message: 'Template must contain at least one operation',
        severity: 'error'
      });
    }
    checksPerformed++;

    // Check operation count
    if (template.operations && template.operations.length > this._config.maxOperationCount) {
      warnings.push(`Template has ${template.operations.length} operations, consider breaking into smaller templates`);
      recommendations.push('Consider splitting large templates into smaller, focused templates');
    }
    checksPerformed++;

    // Validate version format
    if (template.version && !/^\d+\.\d+\.\d+/.test(template.version)) {
      warnings.push('Template version should follow semantic versioning (e.g., 1.0.0)');
    }
    checksPerformed++;

    return { issues, warnings, recommendations, checksPerformed };
  }

  private async _validateOperations(operations: ICleanupTemplateStep[]): Promise<{
    issues: IValidationIssue[];
    warnings: string[];
    recommendations: string[];
    checksPerformed: number;
  }> {
    const issues: IValidationIssue[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let checksPerformed = 0;

    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();

    const operationIds = new Set<string>();

    // ES6+ COMPLIANT: Use forEach instead of for...of
    operations.forEach(operation => {
      // Check for duplicate IDs
      if (operationIds.has(operation.id)) {
        issues.push({
          type: 'duplicate_operation_id',
          message: `Duplicate operation ID: ${operation.id}`,
          severity: 'error'
        });
      }
      operationIds.add(operation.id);
      checksPerformed++;

      // Validate operation structure
      if (!operation.id || operation.id.trim().length === 0) {
        issues.push({
          type: 'missing_operation_id',
          message: 'Operation must have a valid ID',
          severity: 'error'
        });
      }
      checksPerformed++;

      if (!operation.type || operation.type.trim().length === 0) {
        issues.push({
          type: 'missing_operation_type',
          message: `Operation ${operation.id} must have a type`,
          severity: 'error'
        });
      } else if (!this._config.allowedOperationTypes.includes(operation.type)) {
        warnings.push(`Operation ${operation.id} uses non-standard type: ${operation.type}`);
      }
      checksPerformed++;

      // Validate component pattern
      if (!operation.componentPattern || operation.componentPattern.trim().length === 0) {
        warnings.push(`Operation ${operation.id} should have a component pattern`);
      } else {
        try {
          new RegExp(operation.componentPattern);
        } catch (error) {
          issues.push({
            type: 'invalid_component_pattern',
            message: `Operation ${operation.id} has invalid regex pattern: ${operation.componentPattern}`,
            severity: 'error'
          });
        }
      }
      checksPerformed++;

      // Validate estimated duration
      if (operation.estimatedDuration <= 0) {
        warnings.push(`Operation ${operation.id} has invalid estimated duration: ${operation.estimatedDuration}`);
      }
      checksPerformed++;
    });

    return { issues, warnings, recommendations, checksPerformed };
  }

  private _validateStepCondition(condition: IStepCondition): { valid: boolean; reason?: string } {
    if (!condition.type) {
      return { valid: false, reason: 'Condition must have a type' };
    }

    const validTypes = ['always', 'never', 'on_success', 'on_failure', 'custom'];
    if (!validTypes.includes(condition.type)) {
      return { valid: false, reason: `Invalid condition type: ${condition.type}` };
    }

    if (condition.type === 'custom' && !condition.customCondition) {
      return { valid: false, reason: 'Custom condition must have a customCondition function' };
    }

    return { valid: true };
  }

  private _calculateDependencyComplexity(template: ICleanupTemplate): number {
    const graph = new DependencyGraph();
    
    template.operations.forEach(op => {
      graph.addNode(op.id);
      if (op.dependsOn && op.dependsOn.length > 0) {
        graph.addDependency(op.id, op.dependsOn);
      }
    });

    const metrics = graph.getGraphMetrics();
    return metrics.edgeCount + (metrics.maxDepth * 2) + (metrics.cycleCount * 10);
  }

  private _calculateQualityScore(
    template: ICleanupTemplate,
    issues: IValidationIssue[],
    warnings: string[]
  ): number {
    let score = 100;

    // Deduct for errors - more severe penalty
    const errors = issues.filter(i => i.severity === 'error');
    score -= errors.length * 25;

    // Deduct for warnings - increased penalty
    score -= warnings.length * 10;

    // Additional penalty for structural issues
    if (!template.description || template.description.trim().length === 0) {
      score -= 15; // Extra penalty for missing description
    }

    // Additional penalty for operations with missing IDs
    const operationsWithMissingIds = template.operations?.filter(op => !op.id || op.id.trim().length === 0) || [];
    score -= operationsWithMissingIds.length * 20;

    // Bonus for good practices (reduced to avoid inflating scores)
    if (template.description && template.description.length > 20) score += 3;
    if (template.version) score += 3;
    if (template.rollbackSteps && template.rollbackSteps.length > 0) score += 5;

    return Math.max(0, Math.min(100, score));
  }
}

/**
 * ============================================================================
 * EXPORTED UTILITIES
 * ============================================================================
 */

/**
 * Quick template validation function
 */
export async function validateTemplate(
  template: ICleanupTemplate,
  config?: Partial<ITemplateValidationConfig>
): Promise<IExtendedValidationResult> {
  const validator = new TemplateValidator(config);
  return await validator.validateTemplate(template);
}

/**
 * Condition evaluation for step execution
 */
export function evaluateStepCondition(
  condition: IStepCondition,
  context: IStepExecutionContext
): boolean {
  switch (condition.type) {
    case 'always':
      return true;
    case 'on_success':
      return context.previousResults.size === 0 || 
             Array.from(context.previousResults.values()).every((result: any) => result.success);
    case 'on_failure':
      return Array.from(context.previousResults.values()).some((result: any) => !result.success);
    case 'custom':
      // Safe custom condition evaluation
      if (condition.customCondition) {
        try {
          return condition.customCondition(context);
        } catch (error) {
          return false; // Safe default on error
        }
      }
      return true;
    case 'component_exists':
      // Check if component exists in context
      return condition.componentId ? 
        context.globalContext.targetComponents.includes(condition.componentId) : 
        false;
    case 'resource_available':
      // Resource availability check - safe default for now
      return true;
    default:
      return true;
  }
}

/**
 * Component pattern matching utility
 */
export function findMatchingComponents(pattern: string, components: string[]): string[] {
  try {
    const regex = new RegExp(pattern, 'i');
    return components.filter(component => regex.test(component));
  } catch (error) {
    // Fallback to simple string matching if regex is invalid
    return components.filter(component => 
      component.toLowerCase().includes(pattern.toLowerCase())
    );
  }
} 