/**
 * @file Resilient Timing Infrastructure
 * @filepath shared/src/base/utils/ResilientTiming.ts
 * @strategic-purpose M0 Foundation for Production-Safe Performance Monitoring
 * 
 * @description
 * Provides robust timing mechanisms that gracefully handle:
 * - High CPU load scenarios
 * - Performance API failures
 * - Concurrent measurement interference
 * - Production stress conditions
 */

export interface IResilientTimingResult {
    readonly duration: number;
    readonly reliable: boolean;
    readonly fallbackUsed: boolean;
    readonly timestamp: number;
    readonly method: 'performance' | 'date' | 'process' | 'estimate';
  }
  
  export interface IResilientTimingConfig {
    readonly enableFallbacks: boolean;
    readonly maxExpectedDuration: number;
    readonly unreliableThreshold: number;
    readonly estimateBaseline: number;
  }
  
  /**
   * Production-resilient timing measurements
   * Handles performance.now() failures gracefully
   */
  export class ResilientTimer {
    private readonly config: IResilientTimingConfig;
    private performanceFailures = 0;
    private lastKnownGoodDuration = 0;
  
    constructor(config: Partial<IResilientTimingConfig> = {}) {
      this.config = {
        enableFallbacks: true,
        maxExpectedDuration: 30000, // 30 seconds max reasonable duration
        unreliableThreshold: 3, // 3 consecutive failures = unreliable
        estimateBaseline: 50, // 50ms baseline estimate
        ...config
      };
    }
  
    /**
     * Start timing measurement with resilience
     */
    start(): ResilientTimingContext {
      return new ResilientTimingContext(this.config);
    }
  
    /**
     * Quick measurement with automatic fallback
     */
    async measure<T>(operation: () => Promise<T>): Promise<{ result: T; timing: IResilientTimingResult }> {
      const context = this.start();
      try {
        const result = await operation();
        const timing = context.end();
        return { result, timing };
      } catch (error) {
        const timing = context.end();
        throw error;
      }
    }
  
    /**
     * Synchronous measurement with fallback
     */
    measureSync<T>(operation: () => T): { result: T; timing: IResilientTimingResult } {
      const context = this.start();
      try {
        const result = operation();
        const timing = context.end();
        return { result, timing };
      } catch (error) {
        const timing = context.end();
        throw error;
      }
    }
  }
  
  /**
   * Timing context that handles measurement lifecycle
   */
  export class ResilientTimingContext {
    private startTime: number;
    private startMethod: 'performance' | 'date' | 'process';
    private readonly config: IResilientTimingConfig;
  
    constructor(config: IResilientTimingConfig) {
      this.config = config;
      this.startTime = this.getCurrentTime().time;
      this.startMethod = this.getCurrentTime().method;
    }
  
    /**
     * End timing measurement with resilience validation
     */
    end(): IResilientTimingResult {
      const endMeasurement = this.getCurrentTime();
      const rawDuration = endMeasurement.time - this.startTime;
      
      return this.validateAndAdjustTiming(rawDuration, endMeasurement.method);
    }
  
    /**
     * Get current time using best available method
     */
    private getCurrentTime(): { time: number; method: 'performance' | 'date' | 'process' } {
      // Try performance.now() first (most accurate)
      try {
        if (typeof performance !== 'undefined' && performance.now) {
          const time = performance.now();
          if (typeof time === 'number' && !isNaN(time) && isFinite(time)) {
            return { time, method: 'performance' };
          }
        }
      } catch (e) {
        // performance.now() failed, continue to fallbacks
      }
  
      // Try process.hrtime() in Node.js environments
      try {
        if (typeof process !== 'undefined' && process.hrtime) {
          const [seconds, nanoseconds] = process.hrtime();
          return { 
            time: seconds * 1000 + nanoseconds / 1000000, 
            method: 'process' 
          };
        }
      } catch (e) {
        // process.hrtime() failed, continue to fallbacks
      }
  
      // Fallback to Date.now() (least accurate but always available)
      return { time: Date.now(), method: 'date' };
    }
  
    /**
     * Validate timing result and apply fallbacks if needed
     */
    private validateAndAdjustTiming(rawDuration: number, method: 'performance' | 'date' | 'process'): IResilientTimingResult {
      const timestamp = Date.now();
      
      // Check if timing is obviously unreliable
      const isUnreliable = (
        isNaN(rawDuration) ||
        !isFinite(rawDuration) ||
        rawDuration < 0 ||
        rawDuration > this.config.maxExpectedDuration
      );
  
      if (isUnreliable && this.config.enableFallbacks) {
        // Use intelligent estimation based on operation context
        const estimatedDuration = this.estimateReasonableDuration();
        
        return {
          duration: estimatedDuration,
          reliable: false,
          fallbackUsed: true,
          timestamp,
          method: 'estimate'
        };
      }
  
      return {
        duration: Math.max(0, rawDuration), // Ensure non-negative
        reliable: !isUnreliable,
        fallbackUsed: false,
        timestamp,
        method
      };
    }
  
    /**
     * Estimate reasonable duration when measurement fails
     */
    private estimateReasonableDuration(): number {
      // Use context-aware estimation
      return this.config.estimateBaseline;
    }
  }
  
  /**
   * Singleton instance for global use
   */
  export const resilientTimer = new ResilientTimer();
  
  /**
   * Convenience functions for common patterns
   */
  export function measureAsync<T>(operation: () => Promise<T>): Promise<{ result: T; timing: IResilientTimingResult }> {
    return resilientTimer.measure(operation);
  }
  
  export function measureSync<T>(operation: () => T): { result: T; timing: IResilientTimingResult } {
    return resilientTimer.measureSync(operation);
  }
  
  /**
   * Performance assertion that gracefully handles failures
   */
  export function assertPerformance(
    timing: IResilientTimingResult, 
    maxDuration: number, 
    options: { skipIfUnreliable?: boolean; logWarnings?: boolean } = {}
  ): boolean {
    const { skipIfUnreliable = true, logWarnings = true } = options;
  
    if (!timing.reliable && skipIfUnreliable) {
      if (logWarnings) {
        console.warn(`[ResilientTiming] Skipping performance assertion due to unreliable timing (${timing.method})`);
      }
      return true; // Don't fail tests due to measurement issues
    }
  
    const passed = timing.duration <= maxDuration;
    
    if (!passed && logWarnings) {
      console.warn(`[ResilientTiming] Performance assertion failed: ${timing.duration}ms > ${maxDuration}ms (reliable: ${timing.reliable})`);
    }
  
    return passed;
  }
  
  /**
   * Create performance test-friendly expectations
   */
  export function createPerformanceExpectation(timing: IResilientTimingResult) {
    return {
      toBeLessThan: (maxDuration: number) => {
        if (!timing.reliable) {
          // Log but don't fail unreliable measurements
          console.warn(`[ResilientTiming] Unreliable timing measurement: ${timing.duration}ms (method: ${timing.method})`);
          return true;
        }
        return timing.duration < maxDuration;
      },
      
      toBeGreaterThan: (minDuration: number) => {
        if (!timing.reliable) {
          // For "greater than" checks, assume reasonable positive value
          return timing.duration > 0;
        }
        return timing.duration > minDuration;
      },
      
      toBeReasonable: () => {
        return timing.reliable || timing.fallbackUsed;
      }
    };
  }