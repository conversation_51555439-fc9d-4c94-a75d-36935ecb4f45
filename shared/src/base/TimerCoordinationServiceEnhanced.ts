/**
 * ============================================================================
 * AI CONTEXT: Timer Coordination Service Enhanced - Enterprise Orchestrator
 * Purpose: Streamlined orchestrator delegating to specialized modules
 * Complexity: Simple - Clean delegation pattern with resilient timing
 * AI Navigation: 4 logical sections, clear orchestration pattern
 * ============================================================================
 */

/**
 * @file Timer Coordination Service Enhanced - Refactored Orchestrator
 * @filepath shared/src/base/TimerCoordinationServiceEnhanced.ts
 * @refactored-from TimerCoordinationServiceEnhanced.ts (2,779 lines → ≤800 lines)
 * @component timer-coordination-service-enhanced-orchestrator
 * @tier T0
 * @context foundation-context
 * @category Enhanced-Timer-Orchestrator
 * @created 2025-07-26 22:00:00 +03
 * @authority President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy
 * @governance ADR-foundation-011, DCR-foundation-010
 * @refactoring-phase Phase C - Day 4 Core Service Finalization
 * @target-reduction 71% (2,779 → ≤800 lines)
 */

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import { 
  ResilientTimer
} from './utils/ResilientTiming';

import { 
  ResilientMetricsCollector
} from './utils/ResilientMetrics';

// Import base dependencies
import { TimerCoordinationService } from './TimerCoordinationService';
import { MemorySafeResourceManager } from './MemorySafeResourceManager';
import { SimpleLogger, ILoggingService } from './LoggingMixin';

// Import extracted modules
import { TimerPoolManager } from './timer-coordination/modules/TimerPoolManager';
import { TimerUtilities } from './timer-coordination/modules/TimerUtilities';
import { AdvancedScheduler } from './timer-coordination/modules/AdvancedScheduler';
import { TimerCoordinationPatterns } from './timer-coordination/modules/TimerCoordinationPatterns';
import { PhaseIntegrationManager } from './timer-coordination/modules/PhaseIntegration';

// Import type definitions and configuration
import { 
  ITimerPool,
  ITimerPoolConfig,
  ITimerPoolStatistics,
  IRecurringTimerConfig,
  ITimerGroup,
  ISynchronizationResult,
  IGroupDestructionResult,
  ITimerChainStep,
  ITimerCoordinationServiceEnhancedConfig,
  IAdvancedTimerScheduling,
  ITimerCoordination
} from './timer-coordination/types/TimerTypes';

import { 
  DEFAULT_ENHANCED_CONFIG,
  mergeWithDefaults,
  createResilientTimer,
  createResilientMetricsCollector
} from './timer-coordination/modules/TimerConfiguration';

// ============================================================================
// SECTION 1: ENHANCED TIMER COORDINATION SERVICE ORCHESTRATOR
// AI Context: "Enterprise orchestrator with delegation pattern and resilient timing"
// ============================================================================

export class TimerCoordinationServiceEnhanced extends MemorySafeResourceManager 
  implements ILoggingService, IAdvancedTimerScheduling, ITimerCoordination {
  
  // RESILIENT TIMING INFRASTRUCTURE - Dual-field pattern
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  private _logger: SimpleLogger;
  private _config: ITimerCoordinationServiceEnhancedConfig;
  
  // Base timer service
  private _baseTimerService: TimerCoordinationService;
  
  // Extracted module delegates
  private _poolManager!: TimerPoolManager;
  private _utilities!: TimerUtilities;
  private _scheduler!: AdvancedScheduler;
  private _coordinator!: TimerCoordinationPatterns;
  private _phaseIntegration!: PhaseIntegrationManager;
  
  constructor(config?: Partial<ITimerCoordinationServiceEnhancedConfig>) {
    super({
      maxIntervals: 200,
      maxTimeouts: 100,
      maxCacheSize: 10 * 1024 * 1024, // 10MB for orchestrator
      memoryThresholdMB: 200,
      cleanupIntervalMs: 300000 // 5 minutes
    });
    
    this._logger = new SimpleLogger('TimerCoordinationServiceEnhanced');
    this._config = mergeWithDefaults(config);
    this._baseTimerService = TimerCoordinationService.getInstance();
  }
  
  // ============================================================================
  // SECTION 2: LIFECYCLE MANAGEMENT WITH RESILIENT TIMING
  // AI Context: "Memory-safe initialization and cleanup with module orchestration"
  // ============================================================================
  
  protected async doInitialize(): Promise<void> {
    // CONTEXT-BASED TIMING - Create timing context for initialization
    const initContext = this._resilientTimer.start();
    
    try {
      // Initialize resilient timing infrastructure
      this._resilientTimer = createResilientTimer();
      this._metricsCollector = createResilientMetricsCollector();
      
      // Initialize base timer service (singleton doesn't need initialization)

      // Initialize extracted modules (they will auto-initialize on first use)
      this._utilities = new TimerUtilities();
      this._poolManager = new TimerPoolManager(this._baseTimerService);
      this._scheduler = new AdvancedScheduler(this._baseTimerService, this._utilities, this._config);
      this._coordinator = new TimerCoordinationPatterns(this._baseTimerService, this._utilities, this._config);
      this._phaseIntegration = new PhaseIntegrationManager(this._config);

      // Trigger initialization by calling a simple method on each module
      this._utilities.generateOperationId(); // This will trigger initialization
      this._poolManager.getPoolStatistics('test'); // This will trigger initialization
      // Scheduler and coordinator will initialize on first use
      this._phaseIntegration.isPhase1Enabled(); // This will trigger initialization
      
      // Record successful initialization timing
      const initResult = initContext.end();
      this._metricsCollector.recordTiming('orchestrator_operations', initResult);
      
      this.logInfo('TimerCoordinationServiceEnhanced orchestrator initialized successfully', {
        modulesInitialized: 5,
        baseServiceReady: true,
        resilientTimingEnabled: true,
        operationTime: `${initResult.duration}ms`,
        config: {
          poolingEnabled: this._config.pooling.enabled,
          schedulingEnabled: this._config.scheduling.cronParsingEnabled,
          coordinationEnabled: this._config.coordination.groupingEnabled,
          phase1Enabled: this._config.integration.phase1BufferEnabled,
          phase2Enabled: this._config.integration.phase2EventEnabled
        }
      });
      
    } catch (error) {
      // Record failed initialization timing
      const initResult = initContext.end();
      this._metricsCollector.recordTiming('orchestrator_operations_failed', initResult);
      
      this.logError('TimerCoordinationServiceEnhanced orchestrator initialization failed', error, {
        operationTime: `${initResult.duration}ms`
      });
      
      throw this._enhanceErrorContext(error, { operation: 'initialize' });
    }
  }
  
  protected async doShutdown(): Promise<void> {
    // CONTEXT-BASED TIMING - Create timing context for shutdown
    const shutdownContext = this._resilientTimer.start();
    
    try {
      // Shutdown all modules in reverse order (they will auto-shutdown when needed)
      // The modules will clean up their resources automatically

      // Clear references to modules
      this._phaseIntegration = undefined as any;
      this._coordinator = undefined as any;
      this._scheduler = undefined as any;
      this._poolManager = undefined as any;
      this._utilities = undefined as any;

      // Base timer service is a singleton, no need to shutdown
      
      // Record successful shutdown timing
      const shutdownResult = shutdownContext.end();
      this._metricsCollector.recordTiming('orchestrator_operations', shutdownResult);
      
      this.logInfo('TimerCoordinationServiceEnhanced orchestrator shutdown completed', {
        shutdownTime: `${shutdownResult.duration}ms`,
        modulesShutdown: 5
      });
      
    } catch (error) {
      // Record failed shutdown timing
      const shutdownResult = shutdownContext.end();
      this._metricsCollector.recordTiming('orchestrator_operations_failed', shutdownResult);
      
      this.logError('TimerCoordinationServiceEnhanced orchestrator shutdown failed', error, {
        operationTime: `${shutdownResult.duration}ms`
      });
      
      throw this._enhanceErrorContext(error, { operation: 'shutdown' });
    }
  }
  
  // ============================================================================
  // SECTION 3: TIMER POOL MANAGEMENT DELEGATION
  // AI Context: "Pool management operations delegated to TimerPoolManager"
  // ============================================================================
  
  public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    return this._poolManager.createTimerPool(poolId, config);
  }
  
  public createPooledTimer(
    poolId: string,
    callback: () => void,
    intervalMs: number,
    serviceId: string,
    timerId: string
  ): string {
    return this._poolManager.createPooledTimer(poolId, callback, intervalMs, serviceId, timerId);
  }
  
  public getPoolStatistics(poolId: string): ITimerPoolStatistics | null {
    return this._poolManager.getPoolStatistics(poolId);
  }
  
  public removeFromPool(poolId: string, compositeId: string): boolean {
    return this._poolManager.removeFromPool(poolId, compositeId);
  }

  // ============================================================================
  // SECTION 4: ADVANCED SCHEDULING DELEGATION
  // AI Context: "Advanced scheduling operations delegated to AdvancedScheduler"
  // ============================================================================

  public scheduleRecurringTimer(config: IRecurringTimerConfig): string {
    return this._scheduler.scheduleRecurringTimer(config);
  }

  public scheduleCronTimer(
    cronExpression: string,
    callback: () => void,
    serviceId: string,
    timerId?: string
  ): string {
    return this._scheduler.scheduleCronTimer(cronExpression, callback, serviceId, timerId);
  }

  public scheduleConditionalTimer(
    condition: () => boolean,
    callback: () => void,
    checkInterval: number,
    serviceId: string,
    timerId?: string
  ): string {
    return this._scheduler.scheduleConditionalTimer(condition, callback, checkInterval, serviceId, timerId);
  }

  public scheduleDelayedTimer(
    callback: () => void,
    delayMs: number,
    serviceId: string,
    timerId?: string
  ): string {
    return this._scheduler.scheduleDelayedTimer(callback, delayMs, serviceId, timerId);
  }

  public schedulePriorityTimer(
    callback: () => void,
    priority: number,
    intervalMs: number,
    serviceId: string,
    timerId?: string
  ): string {
    return this._scheduler.schedulePriorityTimer(callback, priority, intervalMs, serviceId, timerId);
  }

  // ============================================================================
  // SECTION 5: TIMER COORDINATION DELEGATION
  // AI Context: "Coordination operations delegated to TimerCoordinationPatterns"
  // ============================================================================

  public createTimerGroup(
    groupId: string,
    timerIds: string[],
    coordinationType?: 'parallel' | 'sequential' | 'conditional'
  ): ITimerGroup {
    return this._coordinator.createTimerGroup(groupId, timerIds, coordinationType);
  }

  public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {
    return this._coordinator.synchronizeTimerGroup(groupId);
  }

  public createTimerChain(steps: ITimerChainStep[]): string {
    return this._coordinator.createTimerChain(steps);
  }

  public createTimerBarrier(
    timers: string[],
    callback: () => void,
    barrierType?: 'all' | 'any' | 'majority'
  ): string {
    return this._coordinator.createTimerBarrier(timers, callback, barrierType);
  }

  public async pauseTimerGroup(groupId: string): Promise<void> {
    return this._coordinator.pauseTimerGroup(groupId);
  }

  public async resumeTimerGroup(groupId: string): Promise<void> {
    return this._coordinator.resumeTimerGroup(groupId);
  }

  public async destroyTimerGroup(groupId: string): Promise<IGroupDestructionResult> {
    return this._coordinator.destroyTimerGroup(groupId);
  }

  // ============================================================================
  // SECTION 6: BASE SERVICE DELEGATION & UTILITIES
  // AI Context: "Base service methods and utility operations"
  // ============================================================================

  // Base TimerCoordinationService methods (backward compatibility)
  public createCoordinatedInterval(
    callback: () => void,
    intervalMs: number,
    serviceId: string,
    timerId?: string
  ): string {
    const finalTimerId = timerId || this._utilities.generateTimerId();
    return this._baseTimerService.createCoordinatedInterval(callback, intervalMs, serviceId, finalTimerId);
  }

  public createCoordinatedTimeout(
    callback: () => void,
    timeoutMs: number,
    serviceId: string,
    timerId?: string
  ): string {
    // TimerCoordinationService doesn't have timeout method, use interval with single execution
    const finalTimerId = timerId || this._utilities.generateTimerId();
    const timeoutCallback = () => {
      callback();
      this.removeCoordinatedTimer(`${serviceId}:${finalTimerId}`);
    };

    return this._baseTimerService.createCoordinatedInterval(timeoutCallback, timeoutMs, serviceId, finalTimerId);
  }

  public removeCoordinatedTimer(compositeId: string): boolean {
    try {
      this._baseTimerService.removeCoordinatedTimer(compositeId);
      return true;
    } catch (error) {
      this.logError('Failed to remove coordinated timer', error, { compositeId });
      return false;
    }
  }

  public getTimerStatistics(): any {
    return this._baseTimerService.getTimerStatistics();
  }

  public getHealthDetails(): Record<string, unknown> {
    return this._baseTimerService.getHealthDetails();
  }

  public clearServiceTimers(serviceId: string): void {
    this._baseTimerService.clearServiceTimers(serviceId);
  }

  public clearAllTimers(): void {
    this._baseTimerService.clearAllTimers();
  }

  // Utility methods
  public generateOperationId(): string {
    return this._utilities.generateOperationId();
  }

  public generateTimerId(): string {
    return this._utilities.generateTimerId();
  }

  public validateCronExpression(expression: string): boolean {
    return this._utilities.validateCronExpression(expression);
  }

  // Phase integration methods
  public isPhase1Enabled(): boolean {
    return this._phaseIntegration.isPhase1Enabled();
  }

  public isPhase2Enabled(): boolean {
    return this._phaseIntegration.isPhase2Enabled();
  }

  public getIntegrationMetrics(): any {
    return this._phaseIntegration.getIntegrationMetrics();
  }

  // Configuration access
  public getConfiguration(): ITimerCoordinationServiceEnhancedConfig {
    return { ...this._config };
  }

  // ============================================================================
  // SECTION 7: HELPER METHODS & ERROR HANDLING
  // AI Context: "Error handling and utility methods"
  // ============================================================================

  private _enhanceErrorContext(error: any, context: any): Error {
    const enhancedError = error instanceof Error ? error : new Error(String(error));
    enhancedError.message = `${enhancedError.message} | Context: ${JSON.stringify(context)}`;
    return enhancedError;
  }

  // ILoggingService implementation
  logInfo(message: string, data?: any): void {
    this._logger.logInfo(message, data);
  }

  logWarning(message: string, data?: any): void {
    this._logger.logWarning(message, data);
  }

  logError(message: string, error?: any, data?: any): void {
    this._logger.logError(message, error, data);
  }

  logDebug(message: string, data?: any): void {
    this._logger.logDebug(message, data);
  }
}
