/**
 * @file Template Validation Test Suite
 * @filepath shared/src/base/__tests__/modules/cleanup/TemplateValidation.test.ts
 * @task-id M-TSK-01.SUB-01.REF-01.TEMPLATE-VALIDATION-TESTS
 * @component template-validation-tests
 * @created 2025-07-25 02:49:40 +03
 * 
 * @description
 * Comprehensive test suite for TemplateValidation providing validation of:
 * - TemplateValidator class functionality and configuration
 * - Comprehensive template structure validation logic
 * - Dependency validation with cycle detection integration
 * - Step condition evaluation and logic validation
 * - Parameter and metadata validation workflows
 * - Quality scoring calculation and validation result aggregation
 * - Extended validation result processing and reporting
 * 
 * PHASE B COMPLIANCE:
 * - 100% test preservation mandate from refactoring plan
 * - Performance requirements: <10ms validation operations
 * - Jest compatibility with proven Phase 5 async yielding patterns
 * - File size target: ≤400 lines per refactoring specifications
 * 
 * LESSONS LEARNED INTEGRATION:
 * - Jest compatibility: Async yielding for validation operations
 * - Memory safety: Proper validator cleanup and resource management
 * - Performance: Optimized validation testing with timing validation
 */

import { 
  TemplateValidator,
  validateTemplate,
  evaluateStepCondition,
  findMatchingComponents,
  ITemplateValidationConfig,
  IExtendedValidationResult
} from '../../../modules/cleanup/TemplateValidation';
import {
  ICleanupTemplate,
  ICleanupTemplateStep,
  IStepCondition,
  IStepExecutionContext
} from '../../../types/CleanupTypes';
import { CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinator';

/**
 * ============================================================================
 * AI CONTEXT: Template Validation Test Suite
 * Purpose: Comprehensive testing of template validation functionality
 * Complexity: Moderate - Multi-layer validation logic and quality assessment
 * AI Navigation: 6 logical sections - Setup, Structure, Dependencies, Conditions, Quality, Performance
 * ============================================================================
 */

/**
 * ============================================================================
 * SECTION 1: TEST SETUP & UTILITIES (Lines 1-80)
 * AI Context: "Test configuration, validator setup, and helper functions"
 * ============================================================================
 */

describe('TemplateValidation', () => {
  let templateValidator: TemplateValidator;

  // Test configuration for strict validation
  const strictConfig: Partial<ITemplateValidationConfig> = {
    strictMode: true,
    validateDependencies: true,
    validateConditions: true,
    validateParameters: true,
    maxOperationCount: 10,
    maxDependencyDepth: 5,
    allowedOperationTypes: [
      'timer-cleanup',
      'event-handler-cleanup', 
      'buffer-cleanup',
      'resource-cleanup',
      'memory-cleanup',
      'shutdown-cleanup'
    ]
  };

  // Helper to create test template
  const createTestTemplate = (overrides: Partial<ICleanupTemplate> = {}): ICleanupTemplate => ({
    id: 'test-template-001',
    name: 'Test Validation Template',
    description: 'Template for validation testing',
    version: '1.0.0',
    operations: [
      {
        id: 'step-001',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: 'test-.*',
        operationName: 'test-cleanup',
        parameters: { testParam: 'testValue' },
        timeout: 5000,
        retryPolicy: {
          maxRetries: 3,
          retryDelay: 1000,
          backoffMultiplier: 2,
          maxRetryDelay: 10000,
          retryOnErrors: ['TIMEOUT']
        },
        dependsOn: [],
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 1000,
        description: 'Test cleanup step'
      }
    ],
    conditions: [],
    rollbackSteps: [],
    metadata: { purpose: 'testing', environment: 'test' },
    tags: ['test', 'validation'],
    createdAt: new Date(),
    modifiedAt: new Date(),
    author: 'Test Suite',
    validationRules: [],
    ...overrides
  });

  // Helper to create step execution context
  const createStepContext = (overrides: Partial<IStepExecutionContext> = {}): IStepExecutionContext => ({
    stepId: 'test-step',
    templateId: 'test-template',
    executionId: 'test-execution',
    componentId: 'test-component',
    parameters: {},
    previousResults: new Map(),
    executionAttempt: 1,
    startTime: new Date(),
    globalContext: {
      executionId: 'test-execution',
      templateId: 'test-template',
      targetComponents: ['test-component'],
      parameters: {},
      systemState: {},
      timestamp: new Date()
    },
    ...overrides
  });

  beforeEach(async () => {
    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();
    templateValidator = new TemplateValidator(strictConfig);
  });

  afterEach(async () => {
    // LESSON LEARNED: Proper resource cleanup
    await Promise.resolve();
  });

  /**
   * ============================================================================
   * SECTION 2: TEMPLATE STRUCTURE VALIDATION (Lines 81-160)
   * AI Context: "Basic template structure and format validation"
   * ============================================================================
   */

  describe('Template Structure Validation', () => {
    it('should validate correct template structure', async () => {
      const template = createTestTemplate();
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(true);
      expect(result.issues.filter(i => i.severity === 'error')).toHaveLength(0);
      expect(result.qualityScore).toBeGreaterThan(70);
    });

    it('should reject template with missing ID', async () => {
      const template = createTestTemplate({ id: '' });
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(false);
      expect(result.issues.some(i => i.type === 'missing_id')).toBe(true);
    });

    it('should reject template with no operations', async () => {
      const template = createTestTemplate({ operations: [] });
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(false);
      expect(result.issues.some(i => i.type === 'no_operations')).toBe(true);
    });

    it('should warn about missing description', async () => {
      const template = createTestTemplate({ description: '' });
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(true); // Warning, not error
      expect(result.warnings.some(w => w.includes('description'))).toBe(true);
    });

    it('should validate operation structure completeness', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: '',  // Missing ID
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: '',  // Missing pattern
            operationName: 'test',
            parameters: {},
            timeout: 0,  // Invalid timeout
            retryPolicy: {
              maxRetries: 3,
              retryDelay: 1000,
              backoffMultiplier: 2,
              maxRetryDelay: 10000,
              retryOnErrors: []
            },
            dependsOn: [],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: -1,  // Invalid duration
            description: ''
          }
        ]
      });
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(false);
      expect(result.issues.some(i => i.type === 'missing_operation_id')).toBe(true);
    });
  });

  /**
   * ============================================================================
   * SECTION 3: DEPENDENCY VALIDATION (Lines 161-220)
   * AI Context: "Template dependency validation and cycle detection"
   * ============================================================================
   */

  describe('Dependency Validation', () => {
    it('should validate correct dependency relationships', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'step-001',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-1',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.HIGH,
            estimatedDuration: 1000,
            description: 'First step'
          },
          {
            id: 'step-002',
            type: CleanupOperationType.MEMORY_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-2',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: ['step-001'],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Second step'
          }
        ]
      });
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(true);
      expect(result.issues.filter(i => i.severity === 'error')).toHaveLength(0);
    });

    it('should detect invalid dependency references', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'step-001',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-1',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: ['non-existent-step'],  // Invalid reference
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Step with invalid dependency'
          }
        ]
      });
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(false);
      expect(result.issues.some(i => i.type === 'invalid_dependency')).toBe(true);
    });

    it('should detect circular dependencies', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'step-001',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-1',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: ['step-002'],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'First step'
          },
          {
            id: 'step-002',
            type: CleanupOperationType.MEMORY_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-2',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: ['step-001'],  // Creates cycle
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Second step'
          }
        ]
      });
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(false);
      expect(result.issues.some(i => i.type === 'dependency_graph_error')).toBe(true);
    });
  });

  /**
   * ============================================================================
   * SECTION 4: CONDITION EVALUATION (Lines 221-300)
   * AI Context: "Step condition evaluation and logic validation"
   * ============================================================================
   */

  describe('Condition Evaluation', () => {
    it('should evaluate always condition correctly', () => {
      const condition: IStepCondition = { type: 'always' };
      const context = createStepContext();
      
      const result = evaluateStepCondition(condition, context);
      
      expect(result).toBe(true);
    });

    it('should evaluate on_success condition correctly', () => {
      const condition: IStepCondition = { type: 'on_success' };
      const context = createStepContext({
        previousResults: new Map([
          ['step1', { success: true, stepId: 'step1', componentId: '', executionTime: 100, result: null, retryCount: 0, skipped: false, rollbackRequired: false }]
        ])
      });
      
      const result = evaluateStepCondition(condition, context);
      
      expect(result).toBe(true);
    });

    it('should evaluate on_failure condition correctly', () => {
      const condition: IStepCondition = { type: 'on_failure' };
      const context = createStepContext({
        previousResults: new Map([
          ['step1', { success: false, stepId: 'step1', componentId: '', executionTime: 100, result: null, retryCount: 0, skipped: false, rollbackRequired: true }]
        ])
      });
      
      const result = evaluateStepCondition(condition, context);
      
      expect(result).toBe(true);
    });

    it('should evaluate component_exists condition correctly', () => {
      const condition: IStepCondition = { 
        type: 'component_exists',
        componentId: 'test-component'
      };
      const context = createStepContext({
        globalContext: {
          executionId: 'test',
          templateId: 'test',
          targetComponents: ['test-component', 'other-component'],
          parameters: {},
          systemState: {},
          timestamp: new Date()
        }
      });
      
      const result = evaluateStepCondition(condition, context);
      
      expect(result).toBe(true);
    });

    it('should validate custom condition requirements', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'step-001',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-1',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: [],
            condition: { type: 'custom' }, // Missing customCondition
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Step with invalid custom condition'
          }
        ]
      });
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.valid).toBe(false);
      expect(result.issues.some(i => i.type === 'missing_condition_expression')).toBe(true);
    });
  });

  /**
   * ============================================================================
   * SECTION 5: QUALITY SCORING & UTILITY FUNCTIONS (Lines 301-380)
   * AI Context: "Quality assessment, component matching, and utility functions"
   * ============================================================================
   */

  describe('Quality Scoring & Utilities', () => {
    it('should calculate quality score based on template completeness', async () => {
      const highQualityTemplate = createTestTemplate({
        description: 'Comprehensive template with detailed description',
        version: '1.2.0',
        rollbackSteps: [
          {
            id: 'rollback-001',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'rollback-cleanup',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.HIGH,
            estimatedDuration: 500,
            description: 'Rollback operation'
          }
        ]
      });
      
      const result = await templateValidator.validateTemplate(highQualityTemplate);
      
      expect(result.qualityScore).toBeGreaterThan(90);
    });

    it('should penalize quality score for validation issues', async () => {
      const lowQualityTemplate = createTestTemplate({
        description: '',  // Missing description
        operations: [
          {
            id: '',  // Invalid ID
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup',
            parameters: {},
            timeout: 5000,
            retryPolicy: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2, maxRetryDelay: 10000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 1000,
            description: 'Test step'
          }
        ]
      });
      
      const result = await templateValidator.validateTemplate(lowQualityTemplate);
      
      expect(result.qualityScore).toBeLessThan(50);
    });

    it('should match components using regex patterns', () => {
      const components = ['test-component-1', 'test-component-2', 'other-component', 'test-service'];
      
      const matches = findMatchingComponents('test-.*', components);
      
      expect(matches).toHaveLength(3);
      expect(matches).toContain('test-component-1');
      expect(matches).toContain('test-component-2');
      expect(matches).toContain('test-service');
    });

    it('should handle invalid regex patterns gracefully', () => {
      const components = ['test-component', 'other-component'];
      
      // Invalid regex pattern
      const matches = findMatchingComponents('[invalid-regex', components);
      
      // Should fallback to string matching
      expect(Array.isArray(matches)).toBe(true);
    });

    it('should use validateTemplate utility function', async () => {
      const template = createTestTemplate();
      
      const result = await validateTemplate(template, strictConfig);
      
      expect(result.valid).toBe(true);
      expect(result.performanceMetrics).toBeDefined();
      expect(result.qualityScore).toBeGreaterThan(0);
    });
  });

  /**
   * ============================================================================
   * SECTION 6: PERFORMANCE & EXTENDED VALIDATION (Lines 381-400)
   * AI Context: "Performance validation and extended result processing"
   * ============================================================================
   */

  describe('Performance & Extended Validation', () => {
    it('should maintain performance requirements during validation', async () => {
      // LESSON LEARNED: Performance timing validation
      const startTime = performance.now();
      
      const template = createTestTemplate();
      const result = await templateValidator.validateTemplate(template);
      
      const executionTime = performance.now() - startTime;
      expect(executionTime).toBeLessThan(10); // <10ms requirement
      
      expect(result.performanceMetrics.validationTime).toBeLessThan(10);
      expect(result.performanceMetrics.checksPerformed).toBeGreaterThan(0);
    });

    it('should provide comprehensive extended validation results', async () => {
      const template = createTestTemplate();
      
      const result = await templateValidator.validateTemplate(template);
      
      expect(result.performanceMetrics).toBeDefined();
      expect(result.recommendations).toBeDefined();
      expect(result.qualityScore).toBeGreaterThan(0);
      expect(typeof result.performanceMetrics.validationTime).toBe('number');
      expect(typeof result.performanceMetrics.checksPerformed).toBe('number');
      expect(typeof result.performanceMetrics.dependencyComplexity).toBe('number');
    });
  });
}); 