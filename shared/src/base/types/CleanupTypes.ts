/**
 * ============================================================================
 * AI CONTEXT: Cleanup Types - Comprehensive Type Definitions
 * Purpose: Core type definitions for enhanced cleanup coordination system
 * Complexity: Moderate - Type definitions with clear domain boundaries
 * AI Navigation: 6 logical sections, type definitions only
 * Dependencies: Base CleanupCoordinator types, MemorySafeResourceManager interfaces
 * ============================================================================
 */

/**
 * @file Cleanup Types
 * @filepath shared/src/base/types/CleanupTypes.ts
 * @task-id M-TSK-01.SUB-01.REF-01.TYPES
 * @component cleanup-types
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-cleanup-type-definitions
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Types
 * @created 2025-07-24 16:32:57 +03
 * @modified 2025-07-24 16:32:57 +03
 *
 * @description
 * Comprehensive type definitions for the enhanced cleanup coordination system:
 * - Component registry interfaces for enterprise cleanup operations
 * - Cleanup template system with reusable workflows and validation
 * - Dependency resolution with cycle detection and optimization
 * - Rollback and recovery system with checkpoint management
 * - Template execution with comprehensive result tracking
 * - Performance metrics and monitoring interfaces
 * - Anti-Simplification Policy compliance with complete type coverage
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-003-enhanced-services-refactoring
 * @governance-dcr DCR-foundation-003-enhanced-services-modularization
 * @governance-status approved
 * @governance-compliance authority-validated
 * @task-compliance M-TSK-01.SUB-01.REF-01.TYPES
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/CleanupCoordinator
 * @enables shared/src/base/modules/cleanup/CleanupTemplateManager
 * @enables shared/src/base/modules/cleanup/DependencyResolver
 * @enables shared/src/base/modules/cleanup/RollbackManager
 * @enables shared/src/base/modules/cleanup/SystemOrchestrator
 * @related-contexts foundation-context, memory-safety-context, cleanup-orchestration-context
 * @governance-impact enhanced-cleanup-coordination, type-safety-enhancement
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-enhanced-types
 * @lifecycle-stage implementation
 * @testing-status type-validated
 * @deployment-ready true
 * @monitoring-enabled false
 * @enhancement-phase phase-4-refactoring
 * @backward-compatibility 100%
 * @anti-simplification-compliant true
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   enhancement-validated: true
 *   anti-simplification-compliant: true
 *   modular-refactoring: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-24) - Initial extraction from CleanupCoordinatorEnhanced.ts
 */

// Import base cleanup types
import { 
  CleanupOperationType,
  CleanupPriority,
  CleanupStatus,
  ICleanupCoordinatorConfig
} from '../CleanupCoordinator';

// ============================================================================
// SECTION 1: COMPONENT REGISTRY INTERFACES (Lines 1-100)
// AI Context: "Component registry infrastructure for enterprise cleanup operations"
// ============================================================================

/**
 * Component registry for managing cleanup operations
 */
export interface IComponentRegistry {
  findComponents(pattern?: string): Promise<string[]>;
  getCleanupOperation(operationName: string): CleanupOperationFunction | undefined;
  registerOperation(name: string, operation: CleanupOperationFunction): boolean;
  hasOperation(operationName: string): boolean;
  listOperations(): string[];
  getOperationMetrics(operationName?: string): IOperationMetrics;
}

/**
 * Cleanup operation function signature
 */
export type CleanupOperationFunction = (component: string, params: any) => Promise<ICleanupOperationResult>;

/**
 * Cleanup operation result
 */
export interface ICleanupOperationResult {
  success: boolean;
  duration: number;
  component: string;
  operation: string;
  params?: any;
  cleaned?: string[];
  optimized?: boolean;
  rolledBack?: string[];
  timestamp: Date;
  [key: string]: any;
}

/**
 * Operation metrics
 */
export interface IOperationMetrics {
  totalOperations: number;
  executionCount: number;
  averageExecutionTime: number;
  successRate?: number;
  lastExecution?: Date;
}

// ============================================================================
// SECTION 2: CLEANUP TEMPLATE SYSTEM INTERFACES (Lines 101-200)
// AI Context: "Template system for reusable cleanup workflows and validation"
// ============================================================================

/**
 * Cleanup template system interfaces for reusable workflows
 */
export interface ICleanupTemplate {
  id: string;
  name: string;
  description: string;
  version: string;
  operations: ICleanupTemplateStep[];
  conditions: ICleanupCondition[];
  rollbackSteps: ICleanupTemplateStep[];
  metadata: Record<string, any>;
  tags: string[];
  createdAt: Date;
  modifiedAt: Date;
  author: string;
  validationRules: ITemplateValidationRule[];
}

export interface ICleanupTemplateStep {
  id: string;
  type: CleanupOperationType;
  componentPattern: string; // Regex pattern for component matching
  operationName: string; // Template operation name
  parameters: Record<string, any>;
  timeout: number;
  retryPolicy: IRetryPolicy;
  dependsOn: string[]; // Step IDs this step depends on
  condition?: IStepCondition;
  rollbackOperation?: string;
  priority: CleanupPriority;
  estimatedDuration: number; // milliseconds
  description: string;
}

export interface IRetryPolicy {
  maxRetries: number;
  retryDelay: number; // milliseconds
  backoffMultiplier: number;
  maxRetryDelay: number;
  retryOnErrors: string[]; // Error types to retry on
}

export interface IStepCondition {
  type: 'always' | 'on_success' | 'on_failure' | 'custom' | 'component_exists' | 'resource_available';
  customCondition?: (context: IStepExecutionContext) => boolean;
  componentId?: string;
  resourceType?: string;
  resourceThreshold?: number;
}

export interface ICleanupCondition {
  type: 'system_health' | 'resource_usage' | 'component_state' | 'custom';
  condition: (context: ITemplateExecutionContext) => boolean;
  description: string;
  required: boolean;
}

export interface ITemplateValidationRule {
  type: 'dependency_check' | 'resource_validation' | 'component_compatibility' | 'custom';
  validator: (template: ICleanupTemplate) => IValidationResult;
  description: string;
  severity: 'error' | 'warning' | 'info';
}

export interface IValidationResult {
  valid: boolean;
  issues: IValidationIssue[];
  warnings: string[];
  suggestions: string[];
}

export interface IValidationIssue {
  type: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  stepId?: string;
  field?: string;
}

// ============================================================================
// SECTION 3: TEMPLATE EXECUTION INTERFACES (Lines 201-280)
// AI Context: "Template execution context and result tracking interfaces"
// ============================================================================

/**
 * Template execution interfaces
 */
export interface ITemplateExecution {
  id: string;
  templateId: string;
  targetComponents: string[];
  parameters: Record<string, any>;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  stepResults: Map<string, IStepExecutionResult>;
  rollbackExecuted: boolean;
  error?: Error;
  metrics: ITemplateExecutionMetrics;
}

export interface IStepExecutionContext {
  stepId: string;
  templateId: string;
  executionId: string;
  componentId: string;
  parameters: Record<string, any>;
  previousResults: Map<string, any>;
  executionAttempt: number;
  startTime: Date;
  globalContext: ITemplateExecutionContext;
}

export interface ITemplateExecutionContext {
  executionId: string;
  templateId: string;
  targetComponents: string[];
  parameters: Record<string, any>;
  systemState: Record<string, any>;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

export interface IStepExecutionResult {
  stepId: string;
  componentId: string;
  success: boolean;
  executionTime: number;
  result: any;
  error?: Error;
  retryCount: number;
  skipped: boolean;
  rollbackRequired: boolean;
}

export interface ITemplateExecutionResult {
  executionId: string;
  templateId: string;
  status: 'success' | 'failure' | 'partial';
  executedSteps: number;
  totalSteps: number;
  failedSteps: number;
  skippedSteps: number;
  executionTime: number;
  results: IStepExecutionResult[];
  rollbackExecuted: boolean;
  warnings: string[];
  errors: Error[];
}

export interface ITemplateExecutionMetrics {
  totalSteps: number;
  executedSteps: number;
  failedSteps: number;
  skippedSteps: number;
  averageStepTime: number;
  longestStepTime: number;
  dependencyResolutionTime: number;
  validationTime: number;
  totalExecutionTime: number;
}

// ============================================================================
// SECTION 4: DEPENDENCY RESOLUTION INTERFACES (Lines 281-350)
// AI Context: "Advanced dependency graph system with cycle detection and optimization"
// ============================================================================

/**
 * Advanced dependency resolution interfaces
 */
export interface IDependencyGraph {
  nodes: Set<string>;
  edges: Map<string, Set<string>>; // operation -> dependencies
  addNode(operationId: string): void;
  addDependency(operationId: string, dependsOn: string): void;
  removeDependency(operationId: string, dependsOn: string): void;
  resolveDependencies(operationId: string): string[];
  detectCircularDependencies(): string[][];
  optimizeExecutionOrder(operations: string[]): string[];
  getTopologicalSort(): string[];
  getCriticalPath(): string[];
  getParallelGroups(): string[][];
}

export interface IDependencyAnalysis {
  hasCycles: boolean;
  cycles: string[][];
  criticalPath: string[];
  parallelGroups: string[][];
  estimatedExecutionTime: number;
  bottlenecks: string[];
  optimizationOpportunities: IOptimizationOpportunity[];
  riskAssessment: IRiskAssessment;
}

export interface IOptimizationOpportunity {
  type: 'parallelization' | 'dependency_removal' | 'priority_adjustment' | 'resource_optimization';
  description: string;
  estimatedImprovement: number; // percentage
  implementationComplexity: 'low' | 'medium' | 'high';
  riskLevel: 'low' | 'medium' | 'high';
  affectedOperations: string[];
}

export interface IRiskAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: IRiskFactor[];
  mitigationStrategies: string[];
  contingencyPlans: string[];
}

export interface IRiskFactor {
  type: 'circular_dependency' | 'resource_contention' | 'timing_constraint' | 'external_dependency';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedOperations: string[];
  likelihood: number; // 0-1
  impact: number; // 0-1
}

// ============================================================================
// SECTION 5: ROLLBACK AND RECOVERY INTERFACES (Lines 351-450)
// AI Context: "Comprehensive rollback system with checkpoints and state management"
// ============================================================================

/**
 * Rollback and recovery interfaces
 */
export interface ICleanupRollback {
  createCheckpoint(operationId: string, state?: any): Promise<string>;
  rollbackToCheckpoint(checkpointId: string): Promise<IRollbackResult>;
  rollbackOperation(operationId: string): Promise<IRollbackResult>;
  rollbackTemplate(executionId: string): Promise<IRollbackResult>;
  listCheckpoints(): ICheckpoint[];
  cleanupCheckpoints(olderThan: Date): Promise<number>;
  validateRollbackCapability(operationId: string): IRollbackCapabilityResult;
}

export interface ICheckpoint {
  id: string;
  operationId: string;
  executionId?: string;
  templateId?: string;
  timestamp: Date;
  state: any;
  rollbackActions: IRollbackAction[];
  metadata: Record<string, any>;
  dependencies: string[];
  systemSnapshot: ISystemSnapshot;
  checksum: string;
}

export interface IRollbackAction {
  type: 'restore_state' | 'execute_operation' | 'cleanup_resource' | 'notify_component' | 'revert_configuration';
  parameters: Record<string, any>;
  timeout: number;
  critical: boolean; // If true, rollback fails if this action fails
  priority: number;
  estimatedDuration: number;
  description: string;
  componentId?: string;
  resourceId?: string;
}

export interface IRollbackResult {
  checkpointId?: string;
  operationId: string;
  success: boolean;
  actionsExecuted: number;
  actionsFailed: number;
  executionTime: number;
  errors: Error[];
  warnings: string[];
  partialSuccess: boolean;
  rollbackLevel: 'complete' | 'partial' | 'failed';
}

export interface IRollbackCapabilityResult {
  canRollback: boolean;
  checkpointAvailable: boolean;
  rollbackComplexity: 'simple' | 'moderate' | 'complex';
  estimatedRollbackTime: number;
  riskLevel: 'low' | 'medium' | 'high';
  requirements: string[];
  limitations: string[];
}

export interface ISystemSnapshot {
  timestamp: Date;
  componentStates: Map<string, any>;
  resourceStates: Map<string, any>;
  configurationStates: Map<string, any>;
  activeOperations: string[];
  systemMetrics: Record<string, number>;
  version: string;
}

export interface IRollbackExecution {
  checkpointId: string;
  timestamp: Date;
  result: IRollbackResult;
  triggeredBy: 'manual' | 'automatic' | 'error_handler';
  reason: string;
}

// ============================================================================
// SECTION 6: CONFIGURATION AND UTILITY INTERFACES (Lines 451-500)
// AI Context: "Enhanced configuration and filtering interfaces"
// ============================================================================

/**
 * Enhanced configuration interface
 */
export interface IEnhancedCleanupConfig extends ICleanupCoordinatorConfig {
  templateValidationEnabled?: boolean;
  dependencyOptimizationEnabled?: boolean;
  rollbackEnabled?: boolean;
  maxCheckpoints?: number;
  checkpointRetentionDays?: number;
  phaseIntegrationEnabled?: boolean;
  performanceMonitoringEnabled?: boolean;
}

/**
 * Template execution options
 */
export interface ITemplateExecutionOptions {
  createCheckpoint?: boolean;
  skipValidation?: boolean;
  parallelExecution?: boolean;
  timeoutOverride?: number;
  retryOverride?: IRetryPolicy;
  metadata?: Record<string, any>;
}

/**
 * Template filtering options
 */
export interface ITemplateFilter {
  tags?: string[];
  operationType?: CleanupOperationType;
  author?: string;
  namePattern?: string;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * Checkpoint filtering options
 */
export interface ICheckpointFilter {
  operationId?: string;
  templateId?: string;
  since?: Date;
  until?: Date;
} 