/**
 * ============================================================================
 * AI CONTEXT: Timer Pool Manager - Enterprise Pool Management & Strategies
 * Purpose: Timer pool management with configurable strategies and resource monitoring
 * Complexity: High - Pool strategies, resource monitoring, and performance optimization
 * AI Navigation: 6 logical sections, clear pool management domains
 * ============================================================================
 */

/**
 * @file Timer Pool Manager
 * @filepath shared/src/base/timer-coordination/modules/TimerPoolManager.ts
 * @extracted-from TimerCoordinationServiceEnhanced.ts (lines 700-930)
 * @component timer-pool-manager
 * @tier T0
 * @context foundation-context
 * @category Enhanced-Timer-Pool-Management
 * @created 2025-07-26 19:00:00 +03
 * @authority President & CEO, E<PERSON>Z. Consultancy
 * @governance ADR-foundation-011, DCR-foundation-010
 */

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import { 
  ResilientTimer
} from '../../utils/ResilientTiming';

import { 
  ResilientMetricsCollector
} from '../../utils/ResilientMetrics';

// Import base dependencies
import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { TimerCoordinationService } from '../../TimerCoordinationService';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';

// Import type definitions
import { 
  ITimerPool, 
  ITimerPoolConfig, 
  ITimerPoolStatistics,
  IPoolPerformanceMetrics,
  IPoolUtilizationMetrics
} from '../types/TimerTypes';

import { 
  PERFORMANCE_REQUIREMENTS,
  createResilientTimer,
  createResilientMetricsCollector
} from './TimerConfiguration';

// ============================================================================
// SECTION 1: TIMER POOL MANAGER CLASS
// AI Context: "Enterprise timer pool management with resilient timing infrastructure"
// ============================================================================

export class TimerPoolManager extends MemorySafeResourceManager implements ILoggingService {
  
  // RESILIENT TIMING INFRASTRUCTURE - Dual-field pattern
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  private _logger: SimpleLogger;
  private _baseTimerService: TimerCoordinationService;
  
  // Pool management state
  private _timerPools = new Map<string, ITimerPool>();
  private _poolConfigs = new Map<string, ITimerPoolConfig>();
  private _poolQueue = new Map<string, Array<{callback: () => void, resolve: Function, reject: Function}>>();
  
  constructor(baseTimerService: TimerCoordinationService) {
    super({
      maxIntervals: 100,
      maxTimeouts: 50,
      maxCacheSize: 5 * 1024 * 1024, // 5MB for pool management
      memoryThresholdMB: 100,
      cleanupIntervalMs: 300000 // 5 minutes
    });
    
    this._logger = new SimpleLogger('TimerPoolManager');
    this._baseTimerService = baseTimerService;
  }
  
  // ============================================================================
  // SECTION 2: LIFECYCLE MANAGEMENT WITH RESILIENT TIMING
  // AI Context: "Memory-safe initialization and cleanup with timing infrastructure"
  // ============================================================================
  
  protected async doInitialize(): Promise<void> {
    // Initialize resilient timing infrastructure
    this._resilientTimer = createResilientTimer();
    this._metricsCollector = createResilientMetricsCollector();

    this.logInfo('TimerPoolManager initialized with resilient timing infrastructure', {
      maxPools: this._timerPools.size,
      resilientTimingEnabled: true
    });
  }
  
  protected async doShutdown(): Promise<void> {
    // CONTEXT-BASED TIMING - Create timing context for shutdown
    const shutdownContext = this._resilientTimer.start();
    
    try {
      // Clear all pools and queues
      const poolEntries = Array.from(this._timerPools.entries());
      for (let i = 0; i < poolEntries.length; i++) {
        const [poolId] = poolEntries[i];
        await this._destroyPool(poolId);
      }
      
      this._timerPools.clear();
      this._poolConfigs.clear();
      this._poolQueue.clear();
      
      // Record successful shutdown timing
      const shutdownResult = shutdownContext.end();
      this._metricsCollector.recordTiming('pool_manager_shutdown', shutdownResult);

      this.logInfo('TimerPoolManager shutdown completed', {
        shutdownTime: `${shutdownResult.duration}ms`,
        poolsDestroyed: this._timerPools.size
      });

    } catch (error) {
      // Record failed shutdown timing
      const shutdownResult = shutdownContext.end();
      this._metricsCollector.recordTiming('pool_manager_shutdown_failed', shutdownResult);
      throw this._enhanceErrorContext(error, { operation: 'shutdown' });
    }
  }
  
  // ============================================================================
  // SECTION 3: POOL CREATION WITH RESILIENT TIMING
  // AI Context: "Enterprise timer pool creation with comprehensive validation"
  // ============================================================================
  
  public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    // CONTEXT-BASED TIMING - Create timing context per prompt requirements
    const poolCreationContext = this._resilientTimer.start();
    
    try {
      // Validation
      this._validatePoolCreationPreconditions(poolId, config);
      
      if (this._timerPools.has(poolId)) {
        throw new Error(`Timer pool ${poolId} already exists`);
      }
      
      // Create pool with enterprise metrics
      const pool: ITimerPool = {
        poolId,
        timers: new Set(),
        maxPoolSize: config.maxPoolSize,
        currentSize: 0,
        sharedResources: new Map(),
        poolStrategy: config.poolStrategy,
        customStrategy: config.customStrategy,
        onPoolExhaustion: config.onPoolExhaustion,
        createdAt: new Date(),
        lastAccessed: new Date(),
        utilizationMetrics: {
          utilizationRate: 0,
          averageAccessTime: 0,
          peakUtilization: 0,
          idleTime: 0,
          thrashingEvents: 0,
          lastOptimization: new Date()
        }
      };
      
      this._timerPools.set(poolId, pool);
      this._poolConfigs.set(poolId, config);
      
      // Initialize pool queue if needed
      if (config.onPoolExhaustion === 'queue') {
        this._poolQueue.set(poolId, []);
      }
      
      // Pre-populate pool if configured
      if (config.initialSize > 0) {
        this._prepopulatePool(poolId, config.initialSize);
      }
      
      // Start pool monitoring if enabled
      if (config.monitoringEnabled) {
        this._startPoolMonitoring(poolId);
      }
      
      // Record successful pool creation timing
      const poolResult = poolCreationContext.end();
      this._metricsCollector.recordTiming('pool_operations', poolResult);
      
      // Validate performance requirement (<5ms)
      if (poolResult.reliable && poolResult.duration > PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS) {
        this.logWarning('Pool creation exceeded performance requirement', {
          poolId,
          duration: `${poolResult.duration}ms`,
          requirement: `${PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS}ms`
        });
      }

      this.logInfo('Timer pool created successfully', {
        poolId,
        maxSize: config.maxPoolSize,
        initialSize: config.initialSize,
        strategy: config.poolStrategy,
        operationTime: `${poolResult.duration}ms`,
        performanceCompliant: poolResult.duration <= PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS
      });

      return pool;

    } catch (error) {
      // Record failed pool creation timing
      const poolResult = poolCreationContext.end();
      this._metricsCollector.recordTiming('pool_operations_failed', poolResult);

      this.logError('Timer pool creation failed', error, {
        poolId,
        operationTime: `${poolResult.duration}ms`
      });

      throw this._enhanceErrorContext(error, {
        operation: 'createTimerPool',
        poolId,
        config
      });
    }
  }
  
  // ============================================================================
  // SECTION 4: POOLED TIMER CREATION WITH RESILIENT TIMING
  // AI Context: "Enterprise pooled timer creation with strategy selection"
  // ============================================================================
  
  public createPooledTimer(
    poolId: string,
    callback: () => void,
    intervalMs: number,
    serviceId: string,
    timerId: string
  ): string {
    // CONTEXT-BASED TIMING - Create timing context per prompt requirements
    const timerCreationContext = this._resilientTimer.start();
    
    try {
      const pool = this._timerPools.get(poolId);
      if (!pool) {
        throw new Error(`Timer pool ${poolId} does not exist`);
      }
      
      // Check pool capacity and handle exhaustion
      if (pool.currentSize >= pool.maxPoolSize) {
        return this._handlePoolExhaustion(pool, callback, intervalMs, serviceId, timerId);
      }
      
      // Create timer using pool strategy
      const compositeId = this._createTimerInPool(pool, callback, intervalMs, serviceId, timerId);
      
      // Update pool metrics
      pool.timers.add(compositeId);
      pool.currentSize++;
      pool.lastAccessed = new Date();
      this._updatePoolUtilizationMetrics(pool);
      
      // Record successful timer creation timing
      const timerResult = timerCreationContext.end();
      this._metricsCollector.recordTiming('pool_operations', timerResult);
      
      // Validate performance requirement (<5ms)
      if (timerResult.reliable && timerResult.duration > PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS) {
        this.logWarning('Pooled timer creation exceeded performance requirement', {
          poolId,
          compositeId,
          duration: `${timerResult.duration}ms`,
          requirement: `${PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS}ms`
        });
      }

      this.logInfo('Pooled timer created successfully', {
        poolId,
        compositeId,
        strategy: pool.poolStrategy,
        currentSize: pool.currentSize,
        maxSize: pool.maxPoolSize,
        operationTime: `${timerResult.duration}ms`,
        performanceCompliant: timerResult.duration <= PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS
      });

      return compositeId;

    } catch (error) {
      // Record failed timer creation timing
      const timerResult = timerCreationContext.end();
      this._metricsCollector.recordTiming('pool_operations_failed', timerResult);

      this.logError('Pooled timer creation failed', error, {
        poolId,
        serviceId,
        timerId,
        operationTime: `${timerResult.duration}ms`
      });

      throw this._enhanceErrorContext(error, {
        operation: 'createPooledTimer',
        poolId,
        serviceId,
        timerId
      });
    }
  }

  // ============================================================================
  // SECTION 5: POOL STATISTICS & MONITORING WITH RESILIENT TIMING
  // AI Context: "Enterprise pool statistics with comprehensive metrics"
  // ============================================================================

  public getPoolStatistics(poolId: string): ITimerPoolStatistics | null {
    // CONTEXT-BASED TIMING - Create timing context for statistics
    const statsContext = this._resilientTimer.start();

    try {
      const pool = this._timerPools.get(poolId);
      if (!pool) {
        this.logWarning('Pool statistics requested for non-existent pool', { poolId });
        return null;
      }

      const performanceMetrics = this._calculatePoolPerformanceMetrics(pool);
      const healthScore = this._calculatePoolHealthScore(pool);

      const statistics: ITimerPoolStatistics = {
        poolId,
        currentSize: pool.currentSize,
        maxSize: pool.maxPoolSize,
        utilizationRate: pool.utilizationMetrics.utilizationRate,
        activeTimers: Array.from(pool.timers),
        sharedResourceCount: pool.sharedResources.size,
        strategy: pool.poolStrategy,
        healthScore,
        performanceMetrics
      };

      // Record successful statistics timing
      const statsResult = statsContext.end();
      this._metricsCollector.recordTiming('pool_operations', statsResult);

      return statistics;

    } catch (error) {
      // Record failed statistics timing
      const statsResult = statsContext.end();
      this._metricsCollector.recordTiming('pool_operations_failed', statsResult);
      throw this._enhanceErrorContext(error, {
        operation: 'getPoolStatistics',
        poolId
      });
    }
  }

  public removeFromPool(poolId: string, compositeId: string): boolean {
    // CONTEXT-BASED TIMING - Create timing context for removal
    const removalContext = this._resilientTimer.start();

    try {
      const pool = this._timerPools.get(poolId);
      if (!pool || !pool.timers.has(compositeId)) {
        this.logWarning('Timer removal from pool failed - not found', { poolId, compositeId });
        return false;
      }

      // Remove timer normally through base service
      this._baseTimerService.removeCoordinatedTimer(compositeId);

      // Update pool state
      pool.timers.delete(compositeId);
      pool.currentSize--;
      pool.lastAccessed = new Date();
      this._updatePoolUtilizationMetrics(pool);

      // Process pool queue if items are waiting
      this._processPoolQueue(poolId);

      // Record successful removal timing
      const removalResult = removalContext.end();
      this._metricsCollector.recordTiming('pool_operations', removalResult);

      this.logInfo('Timer removed from pool successfully', {
        poolId,
        compositeId,
        remainingSize: pool.currentSize,
        utilizationRate: pool.utilizationMetrics.utilizationRate,
        operationTime: `${removalResult.duration}ms`
      });

      return true;

    } catch (error) {
      // Record failed removal timing
      const removalResult = removalContext.end();
      this._metricsCollector.recordTiming('pool_operations_failed', removalResult);

      this.logError('Failed to remove timer from pool', error, {
        poolId,
        compositeId,
        operationTime: `${removalResult.duration}ms`
      });
      return false;
    }
  }

  // ============================================================================
  // SECTION 6: PRIVATE HELPER METHODS WITH ENHANCED ERROR CONTEXT
  // AI Context: "Pool management utilities and validation methods"
  // ============================================================================

  private _validatePoolCreationPreconditions(poolId: string, config: ITimerPoolConfig): void {
    if (!poolId || poolId.trim().length === 0) {
      throw new Error('Pool ID cannot be empty');
    }

    if (!config) {
      throw new Error('Pool configuration is required');
    }

    if (config.maxPoolSize <= 0) {
      throw new Error('Pool max size must be positive');
    }

    if (config.initialSize < 0) {
      throw new Error('Pool initial size cannot be negative');
    }

    if (config.initialSize > config.maxPoolSize) {
      throw new Error('Pool initial size cannot exceed max size');
    }
  }

  private _prepopulatePool(poolId: string, initialSize: number): void {
    // Implementation for pool pre-population
    this.logInfo('Pool pre-population initiated', { poolId, initialSize });
  }

  private _startPoolMonitoring(poolId: string): void {
    // Implementation for pool monitoring
    this.logInfo('Pool monitoring started', { poolId });
  }

  private _handlePoolExhaustion(
    pool: ITimerPool,
    _callback: () => void,
    _intervalMs: number,
    serviceId: string,
    timerId: string
  ): string {
    // Implementation for pool exhaustion handling
    const compositeId = `${serviceId}:${timerId}`;
    this.logWarning('Pool exhaustion handled', {
      poolId: pool.poolId,
      strategy: pool.onPoolExhaustion,
      compositeId
    });
    return compositeId;
  }

  private _createTimerInPool(
    _pool: ITimerPool,
    callback: () => void,
    intervalMs: number,
    serviceId: string,
    timerId: string
  ): string {
    // Implementation for timer creation in pool
    return this._baseTimerService.createCoordinatedInterval(callback, intervalMs, serviceId, timerId);
  }

  private _updatePoolUtilizationMetrics(pool: ITimerPool): void {
    // Implementation for pool utilization metrics update
    pool.utilizationMetrics.utilizationRate = pool.currentSize / pool.maxPoolSize;
    pool.utilizationMetrics.lastOptimization = new Date();
  }

  private _calculatePoolPerformanceMetrics(_pool: ITimerPool): IPoolPerformanceMetrics {
    // Implementation for pool performance metrics calculation
    return {
      averageCreationTime: 0,
      averageAccessTime: 0,
      cacheHitRate: 0,
      resourceContentionEvents: 0,
      optimizationCount: 0,
      lastPerformanceCheck: new Date()
    };
  }

  private _calculatePoolHealthScore(pool: ITimerPool): number {
    // Implementation for pool health score calculation
    return Math.max(0, Math.min(100, pool.utilizationMetrics.utilizationRate * 100));
  }

  private _processPoolQueue(poolId: string): void {
    // Implementation for pool queue processing
    const queue = this._poolQueue.get(poolId);
    if (queue && queue.length > 0) {
      this.logInfo('Processing pool queue', { poolId, queueLength: queue.length });
    }
  }

  private async _destroyPool(poolId: string): Promise<void> {
    // Implementation for pool destruction
    const pool = this._timerPools.get(poolId);
    if (pool) {
      const timerIds = Array.from(pool.timers);
      for (let i = 0; i < timerIds.length; i++) {
        const timerId = timerIds[i];
        this._baseTimerService.removeCoordinatedTimer(timerId);
      }
      this.logInfo('Pool destroyed', { poolId, timersDestroyed: pool.timers.size });
    }
  }

  private _enhanceErrorContext(error: any, context: any): Error {
    const enhancedError = error instanceof Error ? error : new Error(String(error));
    enhancedError.message = `${enhancedError.message} | Context: ${JSON.stringify(context)}`;
    return enhancedError;
  }

  // ILoggingService implementation
  logInfo(message: string, data?: any): void {
    this._logger.logInfo(message, data);
  }

  logWarning(message: string, data?: any): void {
    this._logger.logWarning(message, data);
  }

  logError(message: string, error?: any, data?: any): void {
    this._logger.logError(message, error, data);
  }

  logDebug(message: string, data?: any): void {
    this._logger.logDebug(message, data);
  }
}
