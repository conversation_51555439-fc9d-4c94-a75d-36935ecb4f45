oa-prod$ npm test -- --testPathPattern="SystemOrchestrator" --maxWorkers=1

> oa-framework@1.0.0 test
> jest --testPathPattern=SystemOrchestrator --maxWorkers=1

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/modules/cleanup/SystemOrchestrator.test.ts
  ● Test suite failed to run

    shared/src/base/__tests__/modules/cleanup/SystemOrchestrator.test.ts:14:7 - error TS2353: Object literal may only specify known properties, and 'operationTimeout' does not exist in type 'Partial<IEnhancedCleanupConfig>'.

    14       operationTimeout: 30000,
             ~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/modules/cleanup/SystemOrchestrator.test.ts:69:22 - error TS2339: Property 'registerOperationResult' does not exist on type 'SystemOrchestrator'.

    69         orchestrator.registerOperationResult(operationId, result);
                            ~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/modules/cleanup/SystemOrchestrator.test.ts:77:20 - error TS2339: Property 'registerOperationResult' does not exist on type 'SystemOrchestrator'.

    77       orchestrator.registerOperationResult(operationId, result);
                          ~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/modules/cleanup/SystemOrchestrator.test.ts:105:22 - error TS2339: Property 'registerOperationResult' does not exist on type 'SystemOrchestrator'.

    105         orchestrator.registerOperationResult(operationId, result);
                             ~~~~~~~~~~~~~~~~~~~~~~~

Test Suites: 1 failed, 1 total
Tests:       0 total
Snapshots:   0 total
Time:        3.419 s
Ran all test suites matching /SystemOrchestrator/i.
